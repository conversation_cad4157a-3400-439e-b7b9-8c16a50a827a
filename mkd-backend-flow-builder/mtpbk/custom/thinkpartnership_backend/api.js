
const AuthService = require('../../baas/services/AuthService');
const TokenMiddleware = require('../../baas/middleware/TokenMiddleware');

module.exports = function(app) {
  // Middleware to attach SDK to request object for marketplace routes
  app.use('/api/marketplace', (req, res, next) => {
    req.sdk = app.get('sdk');
    next();
  });
  // Get All hidden - route_1734835558351_p7t0wlpbr
  app.get('/api/hidden', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1734835558351_p7t0wlpbr";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');

// Execute database find
    req.sdk.setTable('hidden');
    const hiddenResult = await req.sdk.find({});
    
// Send mock response
    res.status(200).json({
      error: false,
      model: {
  "id": 123,
  "name": "mock_string",
  "code": 123
}
    });
    } catch (error) {
      console.error('Error in Get All hidden:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get One hidden - route_1734835558351_umk8qzgaq
  app.get('/api/hidden/:id', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1734835558351_umk8qzgaq";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');
// Validate URL parameters
    if (!req.params.id) {
      throw new Error('id is required');
    }
// Execute database query
    req.sdk.setTable('hidden');
    const hiddenOneResult = await req.sdk.rawQuery(`SELECT * FROM hidden WHERE id=id`, []);
    
// Send response with model
    res.status(200).json({
      error: false,
      model: hiddenOneResult
    });
    } catch (error) {
      console.error('Error in Get One hidden:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Create hidden - route_1734835558351_vq257bfqw
  app.post('/api/hidden', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1734835558351_vq257bfqw";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');
// Validate request body
// Execute database insert
    req.sdk.setTable('hidden');
    const hiddenCreateResult = await req.sdk.create(req.body);
    
// Send mock response
    res.status(200).json({
      error: false,
      model: {
  "error": false,
  "id": 123
}
    });
    } catch (error) {
      console.error('Error in Create hidden:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update hidden - route_1734835558352_dna6umo13
  app.put('/api/hidden/:id', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1734835558352_dna6umo13";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');
// Validate URL parameters
    if (!req.params.id) {
      throw new Error('id is required');
    }
    // Validate request body
    if (!req.body.name) {
      throw new Error('name is required');
    }
    if (!req.body.code) {
      throw new Error('code is required');
    }
// Execute database update
    req.sdk.setTable('hidden');
    const hiddenUpdateResult = await req.sdk.update({ id: req.params.id }, req.body);
    
// Send mock response
    res.status(200).json({
      error: false,
      model: {
  "error": false,
  "id": 123
}
    });
    } catch (error) {
      console.error('Error in Update hidden:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Delete One hidden - route_1734835558351_578vyr31y
  app.delete('/api/hidden/:id', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1734835558351_578vyr31y";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');
// Validate URL parameters
    if (!req.params.id) {
      throw new Error('id is required');
    }
// Execute database delete
    req.sdk.setTable('hidden');
    const hiddenDeleteResult = await req.sdk.delete({ id: req.params.id });
    
// Send mock response
    res.status(200).json({
      error: false,
      model: {
  "error": false,
  "id": 123
}
    });
    } catch (error) {
      console.error('Error in Delete One hidden:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // this is mock api
  app.post('/api/mockapi', [TokenMiddleware()], async (req, res) => {
    try {
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');
    // Validate query parameters
    if (req.query.a16 !== undefined) {
      
      const req_query_a16 = req.query['a16'];
      if (!Number.isInteger(Number(req_query_a16))) {
        return res.status(400).json({ error: true, message: 'a16 must be an integer' });
      }
    }
    // Validate request body
    if (req.body.a1 !== undefined) {
      const req_body_a1 = req.body['a1'];
      if (typeof req_body_a1 !== 'string') {
        return res.status(400).json({ error: true, message: 'a1 must be a string' });
      }
    }
    if (req.body.a2 !== undefined) {
      const req_body_a2 = req.body['a2'];
      if (!Number.isInteger(Number(req_body_a2))) {
        return res.status(400).json({ error: true, message: 'a2 must be an integer' });
      }
    }
    if (req.body.a3 !== undefined) {
      const req_body_a3 = req.body['a3'];
      if (typeof req_body_a3 !== 'boolean' && req_body_a3 !== 'true' && req_body_a3 !== 'false') {
        return res.status(400).json({ error: true, message: 'a3 must be a boolean' });
      }
    }
    if (req.body.a4 !== undefined) {
      const req_body_a4 = req.body['a4'];
      if (isNaN(Date.parse(req_body_a4))) {
        return res.status(400).json({ error: true, message: 'a4 must be a valid date (YYYY-MM-DD)' });
      }
    }
    if (req.body.a5 !== undefined) {
      const req_body_a5 = req.body['a5'];
      if (isNaN(Date.parse(req_body_a5))) {
        return res.status(400).json({ error: true, message: 'a5 must be a valid datetime (ISO 8601)' });
      }
    }
    if (req.body.a6 !== undefined) {
      const req_body_a6 = req.files?.['a6'];
      if (!req_body_a6) {
        return res.status(400).json({ error: true, message: 'a6 must be a file' });
      }
    }
    if (req.body.a7 !== undefined) {
      const req_body_a7 = req.body['a7'];
      if (!Array.isArray(req_body_a7)) {
        return res.status(400).json({ error: true, message: 'a7 must be an array' });
      }
      for (let i = 0; i < req_body_a7.length; i++) {
        const req_body_a7_item = req_body_a7[i];
        if (typeof req_body_a7_item !== 'string') {
          return res.status(400).json({ error: true, message: `a7[${i}] must be a string` });
        }
      }
    }
    if (req.body.a8 !== undefined) {
      const req_body_a8 = req.body['a8'];
      if (!Array.isArray(req_body_a8)) {
        return res.status(400).json({ error: true, message: 'a8 must be an array' });
      }
      for (let i = 0; i < req_body_a8.length; i++) {
        const req_body_a8_item = req_body_a8[i];
        if (typeof req_body_a8_item !== 'object' || req_body_a8_item === null) {
          return res.status(400).json({ error: true, message: `a8[${i}] must be a valid object` });
        }
        if (req_body_a8_item['a9'] !== undefined) {
        const req_body_a8_item_a9 = req_body_a8_item['a9'];
      if (typeof req_body_a8_item_a9 !== 'string') {
        return res.status(400).json({ error: true, message: 'a9 must be a string' });
      }
      }
      if (req_body_a8_item['a10'] !== undefined) {
        
      }
      const req_body_a8_item_a11 = req_body_a8_item['a11'];
      if (req_body_a8_item_a11 && typeof req_body_a8_item_a11 === 'object') {
        if (req_body_a8_item_a11['a12'] !== undefined) {
        const req_body_a8_item_a11_a12 = req_body_a8_item_a11['a12'];
      if (typeof req_body_a8_item_a11_a12 !== 'string') {
        return res.status(400).json({ error: true, message: 'a12 must be a string' });
      }
      }
      }
      }
    }
    if (req.body.a9 !== undefined) {
      const req_body_a9 = req.body['a9'];
      if (typeof req_body_a9 !== 'object' || req_body_a9 === null) {
        return res.status(400).json({ error: true, message: 'a9 must be an object' });
      }
      if (req_body_a9['a13'] !== undefined) {
        const req_body_a9_a13 = req_body_a9['a13'];
      if (typeof req_body_a9_a13 !== 'string') {
        return res.status(400).json({ error: true, message: 'a13 must be a string' });
      }
      }
      const req_body_a9_a14 = req_body_a9['a14'];
      if (req_body_a9_a14 && typeof req_body_a9_a14 === 'object') {
        if (req_body_a9_a14['a15'] !== undefined) {
        const req_body_a9_a14_a15 = req_body_a9_a14['a15'];
      if (typeof req_body_a9_a14_a15 !== 'string') {
        return res.status(400).json({ error: true, message: 'a15 must be a string' });
      }
      }
      }
    }
// Bearer token authentication
    const isAuthenticated = AuthService.bearer(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'Bearer token authentication failed' });
    }
    // Send mock response
    res.status(201)
       .type('json')
       .json({
  "error": false,
  "model": {
    "message": "mock_string",
    "page": 123,
    "obj": {
      "a16": "mock_string",
      "a17": 123,
      "obj2": {},
      "a18": "mock_string"
    }
  }
});
    } catch (error) {
      console.error('Error in mock:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // a - route_1735711320512_e5xtvptwe
  app.get('/api/a', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1735711320512_e5xtvptwe";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');
// Validate URL parameters
    if (!req.params.id) {
      throw new Error('id is required');
    }
    // Validate request body
// API Key authentication
    const isAuthenticated = AuthService.apiKey(app, req);
    if (!isAuthenticated) {
      return res.status(401).json({ error: true, message: 'API Key authentication failed' });
    }
//step 1 do x
//step 2 do y
// Execute database query
    req.sdk.setTable('company');
    const result = await req.sdk.rawQuery(`SELECT * FROM company WHERE id=:id`, []);
    
// Send response with model
    res.status(201).json({
      error: false,
      model: result
    });
    } catch (error) {
      console.error('Error in a:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get All switchs - route_1736076210652_jefx076w5
  app.get('/api/switchs', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1736076210652_jefx076w5";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');

// No authentication required
// Execute database find
    req.sdk.setTable('switchs');
    const switchResult = await req.sdk.find({});
    
//commented logic
// Send response with list
    res.status(200).json({
      error: false,
      list: switchResult
    });
    } catch (error) {
      console.error('Error in Get All switchs:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get One switchs - route_1736076210652_gr0s2nqxf
  app.get('/api/switchs/:id', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1736076210652_gr0s2nqxf";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');
// Validate URL parameters
    if (!req.params.id) {
      throw new Error('id is required');
    }
// No authentication required
// Execute database query
    req.sdk.setTable('switchs');
    const switchOneResult = await req.sdk.rawQuery(`SELECT * FROM switchs WHERE id=id`, []);
    
//commented logic
// Send response with model
    res.status(200).json({
      error: false,
      model: switchOneResult
    });
    } catch (error) {
      console.error('Error in Get One switchs:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Create switchs - route_1736076210653_ybk49v9ou
  app.post('/api/switchs', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1736076210653_ybk49v9ou";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');
// Validate request body
// No authentication required
// Execute database insert
    req.sdk.setTable('switchs');
    const switchCreateResult = await req.sdk.create(req.body);
    
//commented logic
// Send response with model
    res.status(200).json({
      error: false,
      model: switchCreateResult
    });
    } catch (error) {
      console.error('Error in Create switchs:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Update switchs - route_1736076210653_xs33phrt5
  app.put('/api/switchs/:id', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1736076210653_xs33phrt5";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');
// Validate URL parameters
    if (!req.params.id) {
      throw new Error('id is required');
    }
    // Validate request body
    if (!req.body.label) {
      throw new Error('label is required');
    }
    if (!req.body.options) {
      throw new Error('options is required');
    }
    if (!req.body.user_id) {
      throw new Error('user_id is required');
    }
    if (!req.body.status) {
      throw new Error('status is required');
    }
// No authentication required
// Execute database update
    req.sdk.setTable('switchs');
    const switchUpdateResult = await req.sdk.update({ id: req.params.id }, req.body);
    
//commented logic
// Send response with model
    res.status(200).json({
      error: false,
      model: switchUpdateResult
    });
    } catch (error) {
      console.error('Error in Update switchs:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Delete One switchs - route_1736076210653_m886sh3ha
  app.delete('/api/switchs/:id', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_1736076210653_m886sh3ha";
      const user = req.user;
      
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }
      
      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('testing');
// Validate URL parameters
    if (!req.params.id) {
      throw new Error('id is required');
    }
// No authentication required
// Execute database delete
    req.sdk.setTable('switchs');
    const switchDeleteResult = await req.sdk.delete({ id: req.params.id });
    
//commented logic
// Send response with model
    res.status(200).json({
      error: false,
      model: switchDeleteResult
    });
    } catch (error) {
      console.error('Error in Delete One switchs:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ===== MARKETPLACE AUTHENTICATION APIS =====
  // Refactored to use existing lambda APIs for consistency

  // Customer Registration - Redirect to user lambda register
  app.post('/api/marketplace/auth/register', (req, res) => {
    // Redirect to existing user lambda register API
    res.status(301).json({
      error: false,
      message: 'Please use the user registration endpoint',
      redirect_url: '/v1/api/thinkpartnership/user/lambda/register',
      note: 'Use role_id: "user" for marketplace customers'
    });
  });

  // Customer Login - Redirect to user lambda login
  app.post('/api/marketplace/auth/login', (req, res) => {
    // Redirect to existing user lambda login API
    res.status(301).json({
      error: false,
      message: 'Please use the user login endpoint',
      redirect_url: '/v1/api/thinkpartnership/user/lambda/login',
      note: 'Use role_id: "user" for marketplace customers'
    });
  });

  // Customer Profile - Redirect to user lambda profile
  app.get('/api/marketplace/auth/profile', (req, res) => {
    // Redirect to existing user lambda profile API
    res.status(301).json({
      error: false,
      message: 'Please use the user profile endpoint',
      redirect_url: '/v1/api/thinkpartnership/user/lambda/profile',
      note: 'Include Authorization header with Bearer token'
    });
  });





  // ===== MARKETPLACE SERVICE APIS =====
  // Following lambda format structure from thinkpartnership_backend/lambda/

  const handleGetAllServices = async (req, res, sdk) => {
    try {
      // Set project context for SDK
      sdk.setProjectId('thinkpartnership');

      // Parse query parameters
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 12;
      const offset = (page - 1) * limit;
      const search = req.query.search || '';
      const category = req.query.category || '';
      const location = req.query.location || '';
      const delivery = req.query.delivery || '';
      const sort = req.query.sort || 'best-match';

      // Build WHERE clause
      let whereConditions = ['s.status = 1']; // Only active services
      let params = [];

      // Search filter
      if (search) {
        whereConditions.push('(s.title LIKE ? OR s.description LIKE ? OR s.short_description LIKE ? OR v.business_name LIKE ?)');
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }

      // Category filter
      if (category && category !== 'all') {
        whereConditions.push('c.slug = ?');
        params.push(category);
      }

      // Location filter (province)
      if (location && location !== 'all') {
        whereConditions.push('v.province = ?');
        params.push(location);
      }

      // Delivery time filter
      if (delivery && delivery !== 'all') {
        switch (delivery) {
          case 'same-day':
            whereConditions.push('s.delivery_time LIKE ?');
            params.push('%same day%');
            break;
          case '1-day':
            whereConditions.push('(s.delivery_time LIKE ? OR s.delivery_time LIKE ? OR s.delivery_time LIKE ?)');
            params.push('%same day%', '%1 day%', '%24 hour%');
            break;
          case '3-days':
            whereConditions.push('s.delivery_time NOT LIKE ?');
            params.push('%week%');
            break;
        }
      }

      // Build ORDER BY clause
      let orderBy = 's.created_at DESC'; // Default
      switch (sort) {
        case 'price-low':
          orderBy = 's.base_price ASC';
          break;
        case 'price-high':
          orderBy = 's.base_price DESC';
          break;
        case 'newest':
          orderBy = 's.created_at DESC';
          break;
        case 'popular':
          orderBy = 's.total_orders DESC';
          break;
        case 'rating':
          orderBy = 's.rating DESC';
          break;
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM marketplace_service s
        LEFT JOIN marketplace_vendor v ON s.vendor_id = v.id
        LEFT JOIN service_category c ON s.category_id = c.id
        WHERE ${whereClause}
      `;

      const countResult = await sdk.rawQuery(countQuery, params);
      const total = countResult[0]?.total || 0;

      // Get services with pagination
      const servicesQuery = `
        SELECT
          s.*,
          v.business_name as vendor_name,
          v.logo_image as vendor_image,
          v.rating as vendor_rating,
          v.completed_orders as vendor_completed_orders,
          v.verified as vendor_verified,
          c.name as category_name,
          c.slug as category_slug
        FROM marketplace_service s
        LEFT JOIN marketplace_vendor v ON s.vendor_id = v.id
        LEFT JOIN service_category c ON s.category_id = c.id
        WHERE ${whereClause}
        ORDER BY ${orderBy}
        LIMIT ? OFFSET ?
      `;

      params.push(limit, offset);
      const services = await sdk.rawQuery(servicesQuery, params);

      // Format response
      const formattedServices = services.map(service => ({
        id: service.id,
        title: service.title,
        slug: service.slug,
        description: service.short_description || service.description,
        price: `$${service.base_price}`,
        base_price: service.base_price,
        vendor: service.vendor_name,
        vendor_image: service.vendor_image,
        vendor_rating: service.vendor_rating,
        vendor_verified: service.vendor_verified,
        completed_orders: service.vendor_completed_orders,
        category: service.category_name,
        category_slug: service.category_slug,
        response_time: service.response_time,
        delivery_time: service.delivery_time,
        rating: service.rating,
        total_reviews: service.total_reviews,
        tags: service.tags ? JSON.parse(service.tags) : [],
        images: service.images ? JSON.parse(service.images) : [],
        image: service.images ? JSON.parse(service.images)[0] : null
      }));

      return res.status(200).json({
        error: false,
        services: formattedServices,
        pagination: {
          page: page,
          limit: limit,
          total: total,
          pages: Math.ceil(total / limit)
        },
        filters: {
          search: search,
          category: category,
          location: location,
          delivery: delivery,
          sort: sort
        }
      });

    } catch (error) {
      console.error('Error in Get All Services:', error);
      return res.status(500).json({ error: true, message: error.message });
    }
  };

  // Get All Services with Filtering - route_marketplace_services_list
  app.get('/api/marketplace/services', async (req, res) => {
    await handleGetAllServices(req, res, req.sdk);
  });

  // Get Service Details - route_marketplace_service_details
  app.get('/api/marketplace/services/:id', async (req, res) => {
    try {
      // Set project context for SDK
      req.sdk.setProjectId('thinkpartnership');

      // Validate URL parameters
      if (!req.params.id) {
        throw new Error('Service ID is required');
      }

      // Get service details with vendor and category info
      const serviceQuery = `
        SELECT
          s.*,
          v.business_name as vendor_name,
          v.contact_name as vendor_contact,
          v.phone as vendor_phone,
          v.business_address as vendor_address,
          v.city as vendor_city,
          v.province as vendor_province,
          v.logo_image as vendor_image,
          v.rating as vendor_rating,
          v.completed_orders as vendor_completed_orders,
          v.verified as vendor_verified,
          v.description as vendor_description,
          c.name as category_name,
          c.slug as category_slug
        FROM marketplace_service s
        LEFT JOIN marketplace_vendor v ON s.vendor_id = v.id
        LEFT JOIN service_category c ON s.category_id = c.id
        WHERE s.id = ? AND s.status = 1
      `;

      const serviceResult = await req.sdk.rawQuery(serviceQuery, [req.params.id]);

      if (!serviceResult || serviceResult.length === 0) {
        return res.status(404).json({
          error: true,
          message: 'Service not found'
        });
      }

      const service = serviceResult[0];

      // Get recent reviews for this service
      const reviewsQuery = `
        SELECT
          r.*,
          c.first_name,
          c.last_name
        FROM marketplace_review r
        LEFT JOIN marketplace_customer c ON r.customer_id = c.id
        WHERE r.service_id = ? AND r.status = 1
        ORDER BY r.created_at DESC
        LIMIT 5
      `;

      const reviews = await req.sdk.rawQuery(reviewsQuery, [req.params.id]);

      // Format response
      const formattedService = {
        id: service.id,
        title: service.title,
        slug: service.slug,
        description: service.description,
        short_description: service.short_description,
        base_price: service.base_price,
        pricing_tiers: service.pricing_tiers ? JSON.parse(service.pricing_tiers) : [],
        features: service.features ? JSON.parse(service.features) : [],
        tags: service.tags ? JSON.parse(service.tags) : [],
        images: service.images ? JSON.parse(service.images) : [],
        response_time: service.response_time,
        delivery_time: service.delivery_time,
        service_areas: service.service_areas ? JSON.parse(service.service_areas) : [],
        requirements: service.requirements,
        rating: service.rating,
        total_reviews: service.total_reviews,
        total_orders: service.total_orders,
        vendor: {
          id: service.vendor_id,
          name: service.vendor_name,
          contact: service.vendor_contact,
          phone: service.vendor_phone,
          address: service.vendor_address,
          city: service.vendor_city,
          province: service.vendor_province,
          image: service.vendor_image,
          rating: service.vendor_rating,
          completed_orders: service.vendor_completed_orders,
          verified: service.vendor_verified,
          description: service.vendor_description
        },
        category: {
          id: service.category_id,
          name: service.category_name,
          slug: service.category_slug
        },
        reviews: reviews.map(review => ({
          id: review.id,
          rating: review.rating,
          title: review.title,
          comment: review.comment,
          customer_name: `${review.first_name} ${review.last_name}`,
          created_at: review.created_at,
          would_recommend: review.would_recommend,
          vendor_response: review.vendor_response,
          vendor_response_date: review.vendor_response_date
        }))
      };

      res.status(200).json({
        error: false,
        service: formattedService
      });

    } catch (error) {
      console.error('Error in Get Service Details:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });



  // Get Service Categories - route_marketplace_categories
  app.get('/api/marketplace/categories', async (req, res) => {
    try {
      // Set project context for SDK
      req.sdk.setProjectId('thinkpartnership');

      // Get all active categories
      req.sdk.setTable('service_category');
      const categories = await req.sdk.find({
        status: 1
      }, {
        sort: { sort_order: 1 }
      });

      return res.status(200).json({
        error: false,
        categories: categories
      });

    } catch (error) {
      console.error('Error in Get Categories:', error);
      return res.status(500).json({ error: true, message: error.message });
    }
  });

  // ===== SERVICE REQUEST APIS =====

  // Create Service Request - route_marketplace_service_request
  app.post('/api/marketplace/service-requests', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_marketplace_service_request";
      const user = req.user;

      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }

      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Check if user's role has permission for this route
      if (!RoleClass.permissions?.routes?.includes(routeId)) {
        return res.status(401).json({ error: true, message: 'Access denied' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('thinkpartnership');

      // Validate request body
      if (!req.body.service_id) {
        throw new Error('Service ID is required');
      }
      if (!req.body.customer_name) {
        throw new Error('Customer name is required');
      }
      if (!req.body.customer_email) {
        throw new Error('Customer email is required');
      }
      if (!req.body.customer_phone) {
        throw new Error('Customer phone is required');
      }
      if (!req.body.service_address) {
        throw new Error('Service address is required');
      }
      if (!req.body.service_city) {
        throw new Error('Service city is required');
      }
      if (!req.body.service_postal_code) {
        throw new Error('Service postal code is required');
      }
      if (!req.body.description) {
        throw new Error('Service description is required');
      }

      // Get customer profile
      req.sdk.setTable('marketplace_customer');
      const customer = await req.sdk.findOne({ user_id: req.user_id });

      if (!customer) {
        return res.status(404).json({
          error: true,
          message: 'Customer profile not found'
        });
      }

      // Get service details
      req.sdk.setTable('marketplace_service');
      const service = await req.sdk.findOne({ id: req.body.service_id });

      if (!service) {
        return res.status(404).json({
          error: true,
          message: 'Service not found'
        });
      }

      // Generate unique request number
      const requestNumber = `REQ-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;

      // Create service request
      req.sdk.setTable('service_request');
      const requestData = {
        customer_id: customer.id,
        service_id: req.body.service_id,
        vendor_id: service.vendor_id,
        request_number: requestNumber,
        customer_name: req.body.customer_name,
        customer_email: req.body.customer_email,
        customer_phone: req.body.customer_phone,
        service_address: req.body.service_address,
        service_city: req.body.service_city,
        service_postal_code: req.body.service_postal_code,
        preferred_date: req.body.preferred_date || null,
        preferred_time: req.body.preferred_time || null,
        urgency: req.body.urgency || 3, // Default to flexible
        description: req.body.description,
        selected_tier: req.body.selected_tier || null,
        estimated_price: req.body.estimated_price || null,
        attachments: req.body.attachments ? JSON.stringify(req.body.attachments) : null,
        status: 0 // Pending
      };

      const requestResult = await req.sdk.create(requestData);

      if (!requestResult || requestResult.error) {
        throw new Error('Failed to create service request');
      }

      res.status(201).json({
        error: false,
        message: 'Service request created successfully',
        request: {
          id: requestResult.id,
          request_number: requestNumber,
          status: 'Pending'
        }
      });

    } catch (error) {
      console.error('Error in Create Service Request:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get Customer Service Requests - route_marketplace_customer_requests
  app.get('/api/marketplace/service-requests', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_marketplace_customer_requests";
      const user = req.user;

      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }

      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('thinkpartnership');

      // Get customer profile
      req.sdk.setTable('marketplace_customer');
      const customer = await req.sdk.findOne({ user_id: req.user_id });

      if (!customer) {
        return res.status(404).json({
          error: true,
          message: 'Customer profile not found'
        });
      }

      // Get service requests with service and vendor details
      const requestsQuery = `
        SELECT
          sr.*,
          s.title as service_title,
          s.base_price as service_price,
          v.business_name as vendor_name,
          v.logo_image as vendor_image,
          v.phone as vendor_phone
        FROM service_request sr
        LEFT JOIN marketplace_service s ON sr.service_id = s.id
        LEFT JOIN marketplace_vendor v ON sr.vendor_id = v.id
        WHERE sr.customer_id = ?
        ORDER BY sr.created_at DESC
      `;

      const requests = await req.sdk.rawQuery(requestsQuery, [customer.id]);

      // Format response
      const formattedRequests = requests.map(request => ({
        id: request.id,
        request_number: request.request_number,
        service_title: request.service_title,
        service_price: request.service_price,
        vendor_name: request.vendor_name,
        vendor_image: request.vendor_image,
        vendor_phone: request.vendor_phone,
        status: request.status,
        urgency: request.urgency,
        preferred_date: request.preferred_date,
        preferred_time: request.preferred_time,
        description: request.description,
        vendor_response: request.vendor_response,
        vendor_quote: request.vendor_quote,
        scheduled_date: request.scheduled_date,
        created_at: request.created_at,
        updated_at: request.updated_at
      }));

      res.status(200).json({
        error: false,
        requests: formattedRequests
      });

    } catch (error) {
      console.error('Error in Get Customer Service Requests:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // ===== ORDER MANAGEMENT APIS =====

  // Get Customer Orders - route_marketplace_customer_orders
  app.get('/api/marketplace/orders', [TokenMiddleware()], async (req, res) => {
    try {
      // Check route permissions
      const routeId = "route_marketplace_customer_orders";
      const user = req.user;

      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }

      // Load role class dynamically
      const roleClassName = req.role;
      let RoleClass;
      try {
        RoleClass = require(`./roles/${roleClassName}`);
      } catch (error) {
        console.error(`Role class not found for role: ${roleClassName}`);
        return res.status(401).json({ error: true, message: 'Invalid role access' });
      }

      // Set project context for SDK
      req.sdk.setProjectId('thinkpartnership');

      // Get customer profile
      req.sdk.setTable('marketplace_customer');
      const customer = await req.sdk.findOne({ user_id: req.user_id });

      if (!customer) {
        return res.status(404).json({
          error: true,
          message: 'Customer profile not found'
        });
      }

      // Get orders with service and vendor details
      const ordersQuery = `
        SELECT
          o.*,
          v.business_name as vendor_name,
          v.logo_image as vendor_image,
          v.phone as vendor_phone
        FROM marketplace_order o
        LEFT JOIN marketplace_vendor v ON o.vendor_id = v.id
        WHERE o.customer_id = ?
        ORDER BY o.created_at DESC
      `;

      const orders = await req.sdk.rawQuery(ordersQuery, [customer.id]);

      // Format response to match frontend expectations
      const formattedOrders = orders.map(order => ({
        id: order.order_number,
        service: order.service_title,
        vendor: order.vendor_name,
        date: order.created_at,
        status: getOrderStatusText(order.order_status),
        amount: `$${order.total_amount}`,
        notes: order.customer_notes
      }));

      res.status(200).json({
        error: false,
        orders: formattedOrders
      });

    } catch (error) {
      console.error('Error in Get Customer Orders:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Helper function to convert order status
  function getOrderStatusText(status) {
    const statusMap = {
      0: 'not paid',
      1: 'paid',
      2: 'processing',
      3: 'completed',
      4: 'cancelled',
      5: 'refunded'
    };
    return statusMap[status] || 'unknown';
  }

  // ===== CHAT AND COMMUNICATION APIS =====

  // Get Customer Chats - route_marketplace_customer_chats
  app.get('/api/marketplace/chats', [TokenMiddleware()], async (req, res) => {
    try {
      // Set project context for SDK
      req.sdk.setProjectId('thinkpartnership');

      // Get customer profile
      req.sdk.setTable('marketplace_customer');
      const customer = await req.sdk.findOne({ user_id: req.user_id });

      if (!customer) {
        return res.status(404).json({
          error: true,
          message: 'Customer profile not found'
        });
      }

      // Get chats with vendor details
      const chatsQuery = `
        SELECT
          c.*,
          v.business_name as vendor_name,
          v.logo_image as vendor_image,
          s.title as service_title
        FROM marketplace_chat c
        LEFT JOIN marketplace_vendor v ON c.vendor_id = v.id
        LEFT JOIN marketplace_service s ON c.service_id = s.id
        WHERE c.customer_id = ? AND c.status = 0
        ORDER BY c.last_message_date DESC
      `;

      const chats = await req.sdk.rawQuery(chatsQuery, [customer.id]);

      res.status(200).json({
        error: false,
        chats: chats
      });

    } catch (error) {
      console.error('Error in Get Customer Chats:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get Chat Messages - route_marketplace_chat_messages
  app.get('/api/marketplace/chats/:chatId/messages', [TokenMiddleware()], async (req, res) => {
    try {
      // Set project context for SDK
      req.sdk.setProjectId('thinkpartnership');

      // Validate URL parameters
      if (!req.params.chatId) {
        throw new Error('Chat ID is required');
      }

      // Get customer profile
      req.sdk.setTable('marketplace_customer');
      const customer = await req.sdk.findOne({ user_id: req.user_id });

      if (!customer) {
        return res.status(404).json({
          error: true,
          message: 'Customer profile not found'
        });
      }

      // Verify chat belongs to customer
      req.sdk.setTable('marketplace_chat');
      const chat = await req.sdk.findOne({
        id: req.params.chatId,
        customer_id: customer.id
      });

      if (!chat) {
        return res.status(404).json({
          error: true,
          message: 'Chat not found'
        });
      }

      // Get messages
      req.sdk.setTable('marketplace_message');
      const messages = await req.sdk.find({
        chat_id: req.params.chatId
      }, {
        sort: { created_at: 1 }
      });

      // Mark messages as read by customer
      await req.sdk.update(
        { chat_id: req.params.chatId, read_by_customer: false },
        {
          read_by_customer: true,
          read_at_customer: new Date()
        }
      );

      // Update chat unread count
      req.sdk.setTable('marketplace_chat');
      await req.sdk.update(
        { id: req.params.chatId },
        { unread_customer: 0 }
      );

      res.status(200).json({
        error: false,
        messages: messages
      });

    } catch (error) {
      console.error('Error in Get Chat Messages:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });

  // Send Chat Message - route_marketplace_chat_send
  app.post('/api/marketplace/chats/:chatId/messages', [TokenMiddleware()], async (req, res) => {
    try {
      // Set project context for SDK
      req.sdk.setProjectId('thinkpartnership');

      // Validate URL parameters
      if (!req.params.chatId) {
        throw new Error('Chat ID is required');
      }

      // Validate request body
      if (!req.body.message) {
        throw new Error('Message is required');
      }

      // Get customer profile
      req.sdk.setTable('marketplace_customer');
      const customer = await req.sdk.findOne({ user_id: req.user_id });

      if (!customer) {
        return res.status(404).json({
          error: true,
          message: 'Customer profile not found'
        });
      }

      // Verify chat belongs to customer
      req.sdk.setTable('marketplace_chat');
      const chat = await req.sdk.findOne({
        id: req.params.chatId,
        customer_id: customer.id
      });

      if (!chat) {
        return res.status(404).json({
          error: true,
          message: 'Chat not found'
        });
      }

      // Create message
      req.sdk.setTable('marketplace_message');
      const messageData = {
        chat_id: req.params.chatId,
        sender_id: customer.id,
        sender_type: 0, // Customer
        message: req.body.message,
        message_type: req.body.message_type || 0, // Text
        attachments: req.body.attachments ? JSON.stringify(req.body.attachments) : null,
        read_by_customer: true,
        read_by_vendor: false,
        read_at_customer: new Date()
      };

      const messageResult = await req.sdk.create(messageData);

      if (!messageResult || messageResult.error) {
        throw new Error('Failed to send message');
      }

      // Update chat with last message info
      req.sdk.setTable('marketplace_chat');
      await req.sdk.update(
        { id: req.params.chatId },
        {
          last_message: req.body.message,
          last_message_sender: 0, // Customer
          last_message_date: new Date(),
          unread_vendor: chat.unread_vendor + 1
        }
      );

      res.status(201).json({
        error: false,
        message: 'Message sent successfully',
        message_id: messageResult.id
      });

    } catch (error) {
      console.error('Error in Send Chat Message:', error);
      res.status(500).json({ error: true, message: error.message });
    }
  });
};