{"settings": {"globalKey": "key_1750871559155_kn46t0fkn", "databaseType": "mysql", "authType": "session", "timezone": "UTC", "dbHost": "localhost", "dbPort": "3306", "dbUser": "root", "dbPassword": "root", "dbName": "database_2025-06-25", "id": "project_1750871559156_zkfwpn4re", "isPWA": false, "isMultiTenant": false, "model_namespace": "namespace_1750871559156_cysek2zo2", "payment_option": "none"}, "models": [{"id": "model_1750871583828_3x5syxcmw", "name": "company", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "name", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "website", "type": "string", "defaultValue": "", "validation": ""}, {"name": "logo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "owner_id", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "address", "type": "string", "defaultValue": "", "validation": ""}, {"name": "city", "type": "string", "defaultValue": "", "validation": ""}, {"name": "state", "type": "string", "defaultValue": "", "validation": ""}, {"name": "zip", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "country", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": "phone"}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1750871583828_a6u0qoglp", "name": "user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required,email"}, {"name": "company_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "password", "type": "password", "defaultValue": "", "validation": "required"}, {"name": "login_type", "type": "mapping", "mapping": "0:<PERSON>,1:Google,2:Microsoft,3:Apple,4:Twitter,5:Facebook", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4,5"}, {"name": "role_id", "type": "string", "defaultValue": "", "validation": ""}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "verify", "type": "boolean", "defaultValue": "0", "validation": "required"}, {"name": "two_factor_authentication", "type": "boolean", "defaultValue": "0", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "0", "validation": ""}, {"name": "stripe_uid", "type": "string", "defaultValue": "", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1750871583828_z7ysjzpc4", "name": "company_user", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "role", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "email", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1750871583828_6itbyh27p", "name": "company_admin", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1750871583828_15ozbf2eh", "name": "company_employee_subscription", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "stripe_subscription_id", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "status", "type": "mapping", "mapping": "0:Active,1:Inactive,2:Suspend", "defaultValue": "0", "validation": "required,enum:0,1,2"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1750871583828_cwadd0fmr", "name": "company_usage", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "company_id", "type": "integer", "defaultValue": "", "validation": "required"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1750871583828_hod2tr0vm", "name": "preference", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "first_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "last_name", "type": "string", "defaultValue": "", "validation": ""}, {"name": "phone", "type": "string", "defaultValue": "", "validation": ""}, {"name": "photo", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "integer", "defaultValue": "", "validation": "required"}]}, {"id": "model_1750871583828_knt6sgqsa", "name": "tokens", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": "required"}, {"name": "token", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "code", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Access,1:<PERSON><PERSON><PERSON>,2:<PERSON><PERSON>,3:<PERSON><PERSON><PERSON>,4:<PERSON>", "defaultValue": "0", "validation": "required,enum:0,1,2,3,4"}, {"name": "data", "type": "json", "defaultValue": "", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Inactive,1:Active", "defaultValue": "1", "validation": "required,enum:0,1"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "expired_at", "type": "timestamp", "defaultValue": "", "validation": "date"}]}, {"id": "model_1750871583828_cqrtjk08m", "name": "uploads", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "url", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "caption", "type": "string", "defaultValue": "", "validation": ""}, {"name": "user_id", "type": "foreign key", "defaultValue": "", "validation": ""}, {"name": "width", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "height", "type": "integer", "defaultValue": "", "validation": ""}, {"name": "type", "type": "mapping", "mapping": "0:Image,1:s3,2:Video,3:base64", "defaultValue": "0", "validation": "required,enum:0,1,2,3"}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}, {"id": "model_1750871583828_6htprours", "name": "cms", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "label", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "type", "type": "mapping", "mapping": "0:Text,1:Number,2:Image,3:Raw", "defaultValue": "0", "validation": "required,enum:0,1,2,3"}, {"name": "value", "type": "long text", "defaultValue": "", "validation": "required"}]}, {"id": "model_1750871583828_k5o6xk6qw", "name": "job", "fields": [{"name": "id", "type": "primary key", "defaultValue": "", "validation": ""}, {"name": "task", "type": "string", "defaultValue": "", "validation": "required"}, {"name": "arguments", "type": "json", "defaultValue": "", "validation": ""}, {"name": "time_interval", "type": "string", "defaultValue": "once", "validation": ""}, {"name": "retries", "type": "integer", "defaultValue": "1", "validation": ""}, {"name": "status", "type": "mapping", "mapping": "0:Pending,1:Failed,2:Processing,3:Completed", "defaultValue": "0", "validation": ""}, {"name": "created_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}, {"name": "updated_at", "type": "timestamp", "defaultValue": "CURRENT_TIMESTAMP", "validation": "date"}]}], "roles": [{"id": "role_1750871599384_6rueoqj4c", "name": "admin", "slug": "admin", "permissions": {"routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"company": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_admin": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_employee_subscription": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_usage": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "cms": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}}}, {"id": "role_1750871645257_p3uh<PERSON>a", "name": "client", "slug": "client", "permissions": {"routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"company": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_admin": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_employee_subscription": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_usage": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "cms": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}}}, {"id": "role_1750871690289_5zcdzqwrb", "name": "vendor", "slug": "vendor", "permissions": {"routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"company": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_admin": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_employee_subscription": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_usage": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "cms": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}}}, {"id": "role_1750871743725_sb4u6re57", "name": "user", "slug": "user", "permissions": {"routes": [], "canCreateUsers": true, "canEditUsers": true, "canDeleteUsers": true, "canManageRoles": true, "canLogin": true, "canRegister": true, "canForgot": false, "canReset": false, "canGoogleLogin": true, "canAppleLogin": true, "canMicrosoftLogin": true, "canMagicLinkLogin": false, "canTwitterLogin": false, "needs2FA": true, "canSetPermissions": true, "canPreference": true, "canVerifyEmail": true, "canUpload": true, "canStripe": true, "canStripeWebhook": true, "canRealTime": true, "canAI": true, "canUpdateEmail": true, "canUpdatePassword": true, "canUpdateOtherUsers": true, "treeql": {"enabled": true, "models": {"company": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_user": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_admin": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_employee_subscription": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "company_usage": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "preference": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "tokens": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "uploads": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "cms": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}, "job": {"allowed": true, "blacklistedFields": [], "operations": {"get": true, "getOne": true, "getAll": true, "post": true, "put": true, "delete": true, "paginate": true, "join": true}}}}}}], "routes": []}