CREATE TABLE IF NOT EXISTS thinkpartnership_company (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `name` VA<PERSON>HA<PERSON>(512) NOT NULL,
  `website` VARCHAR(512),
  `logo` VA<PERSON>HA<PERSON>(512),
  `owner_id` INT,
  `address` VA<PERSON><PERSON><PERSON>(512),
  `city` VARCHAR(512),
  `state` VA<PERSON>HAR(512),
  `zip` INT,
  `country` VARCHAR(512),
  `phone` VARCHAR(512),
  `status` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS thinkpartnership_user (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `email` VARCHAR(512) NOT NULL,
  `company_id` INT NOT NULL,
  `password` VARCHAR(100) NOT NULL,
  `login_type` INT DEFAULT 0 NOT NULL,
  `role_id` VARCHAR(512),
  `data` TEXT,
  `status` INT DEFAULT 0 NOT NULL,
  `verify` B<PERSON>OLEAN DEFAULT '0' NOT NULL,
  `two_factor_authentication` BOOLEAN DEFAULT '0',
  `stripe_uid` VARCHAR(512),
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS thinkpartnership_company_user (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `company_id` INT NOT NULL,
  `user_id` INT NOT NULL,
  `role` VARCHAR(512) NOT NULL,
  `email` VARCHAR(512) NOT NULL,
  `status` INT DEFAULT 0 NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS thinkpartnership_company_admin (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `company_id` INT NOT NULL,
  `user_id` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS thinkpartnership_company_employee_subscription (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `company_id` INT NOT NULL,
  `user_id` INT NOT NULL,
  `stripe_subscription_id` VARCHAR(512) NOT NULL,
  `status` INT DEFAULT 0 NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS thinkpartnership_company_usage (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `company_id` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS thinkpartnership_preference (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `first_name` VARCHAR(512),
  `last_name` VARCHAR(512),
  `phone` VARCHAR(512),
  `photo` VARCHAR(512),
  `user_id` INT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS thinkpartnership_tokens (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `user_id` INT NOT NULL,
  `token` VARCHAR(512) NOT NULL,
  `code` VARCHAR(512) NOT NULL,
  `type` INT DEFAULT 0 NOT NULL,
  `data` TEXT,
  `status` INT DEFAULT 1 NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `expired_at` TIMESTAMP
);

CREATE TABLE IF NOT EXISTS thinkpartnership_uploads (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `url` VARCHAR(512) NOT NULL,
  `caption` VARCHAR(512),
  `user_id` INT,
  `width` INT,
  `height` INT,
  `type` INT DEFAULT 0 NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS thinkpartnership_cms (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `label` VARCHAR(512) NOT NULL,
  `type` INT DEFAULT 0 NOT NULL,
  `value` LONGTEXT NOT NULL,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE IF NOT EXISTS thinkpartnership_job (
  `id` INT AUTO_INCREMENT PRIMARY KEY,
  `task` VARCHAR(512) NOT NULL,
  `arguments` TEXT,
  `time_interval` VARCHAR(512) DEFAULT 'once',
  `retries` INT DEFAULT '1',
  `status` INT DEFAULT 0,
  `created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

