


import { useMemo } from "react";
import { operations } from "@/utils";
import TreeSD<PERSON> from "@/utils/TreeSDK";
import MkdSDK from "@/utils/MkdSDK";
import AdminSDK from "@/utils/AdminSDK";
import Client<PERSON><PERSON> from "@/utils/ClientSDK";
import VendorSDK from "@/utils/VendorSDK";
import UserSDK from "@/utils/UserSDK";

interface SdkConfig {
  baseurl?: string;
  fe_baseurl?: string;
  project_id?: string;
  secret?: string;
  table?: string;
}

interface UseSDKReturnType {
  sdk: MkdSDK;
  adminSdk: AdminSDK;
clientSdk: ClientSDK;
vendorSdk: VendorSDK;
userSdk: UserSDK;
  tdk: TreeSDK;
  projectId: string,
  operations: typeof operations;
}

const useSDK = (config: SdkConfig = {}): UseSDKReturnType => {
  const sdk = useMemo(() => {
    return new MkdSDK(config);
  }, [MkdSDK]);
  
  
    const adminSdk = useMemo(() => {
      return new AdminSDK(config);
    }, [AdminSDK]);
    

    const clientSdk = useMemo(() => {
      return new ClientSDK(config);
    }, [ClientSDK]);
    

    const vendorSdk = useMemo(() => {
      return new VendorSDK(config);
    }, [VendorSDK]);
    

    const userSdk = useMemo(() => {
      return new UserSDK(config);
    }, [UserSDK]);
    

  const tdk = useMemo(() => {
    return new TreeSDK(config);
  }, [TreeSDK]);

  const projectId = sdk.getProjectId()

  return { 
  sdk,
  tdk,
  projectId,
  operations, 
  adminSdk,
clientSdk,
vendorSdk,
userSdk
  };
};

export default useSDK;
