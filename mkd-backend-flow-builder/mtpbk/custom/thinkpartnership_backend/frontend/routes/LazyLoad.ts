
import { lazy } from 'react';

export const AdminListCompanyPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListCompanyPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCompanyPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddCompanyPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCompanyPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditCompanyPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCompanyPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewCompanyPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListUserPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListUserPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddUserPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddUserPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditUserPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditUserPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewUserPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewUserPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListCompany_userPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListCompany_userPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCompany_userPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddCompany_userPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCompany_userPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditCompany_userPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCompany_userPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewCompany_userPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListCompany_adminPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListCompany_adminPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCompany_adminPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddCompany_adminPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCompany_adminPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditCompany_adminPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCompany_adminPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewCompany_adminPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListCompany_employee_subscriptionPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListCompany_employee_subscriptionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCompany_employee_subscriptionPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddCompany_employee_subscriptionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCompany_employee_subscriptionPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditCompany_employee_subscriptionPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCompany_employee_subscriptionPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewCompany_employee_subscriptionPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListCompany_usagePage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListCompany_usagePage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCompany_usagePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddCompany_usagePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCompany_usagePage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditCompany_usagePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCompany_usagePage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewCompany_usagePage");
  __import.finally(() => {});
  return __import;
});


export const AdminListPreferencePage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListPreferencePage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddPreferencePage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddPreferencePage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditPreferencePage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditPreferencePage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewPreferencePage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewPreferencePage");
  __import.finally(() => {});
  return __import;
});


export const AdminListTokensPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListTokensPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddTokensPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddTokensPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditTokensPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditTokensPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewTokensPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewTokensPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListUploadsPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListUploadsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddUploadsPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddUploadsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditUploadsPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditUploadsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewUploadsPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewUploadsPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListCmsPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListCmsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddCmsPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddCmsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditCmsPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditCmsPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewCmsPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewCmsPage");
  __import.finally(() => {});
  return __import;
});


export const AdminListJobPage = lazy(() => {
  const __import = import("@/pages/Admin/List/AdminListJobPage");
  __import.finally(() => {});
  return __import;
});

export const AdminAddJobPage = lazy(() => {
  const __import = import("@/pages/Admin/Add/AdminAddJobPage");
  __import.finally(() => {});
  return __import;
});

export const AdminEditJobPage = lazy(() => {
  const __import = import("@/pages/Admin/Edit/AdminEditJobPage");
  __import.finally(() => {});
  return __import;
});

export const AdminViewJobPage = lazy(() => {
  const __import = import("@/pages/Admin/View/AdminViewJobPage");
  __import.finally(() => {});
  return __import;
});
    