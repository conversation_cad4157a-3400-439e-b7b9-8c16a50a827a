

    
import { AdminWrapper } from "Components/AdminWrapper";

import { 

    AdminListCompanyPage,
    AdminAddCompanyPage,
    AdminEditCompanyPage,
    AdminViewCompanyPage,

    AdminListUserPage,
    AdminAddUserPage,
    AdminEditUserPage,
    AdminViewUserPage,

    AdminListCompany_userPage,
    AdminAddCompany_userPage,
    AdminEditCompany_userPage,
    AdminViewCompany_userPage,

    AdminListCompany_adminPage,
    AdminAddCompany_adminPage,
    AdminEditCompany_adminPage,
    AdminViewCompany_adminPage,

    AdminListCompany_employee_subscriptionPage,
    AdminAddCompany_employee_subscriptionPage,
    AdminEditCompany_employee_subscriptionPage,
    AdminViewCompany_employee_subscriptionPage,

    AdminListCompany_usagePage,
    AdminAddCompany_usagePage,
    AdminEditCompany_usagePage,
    AdminViewCompany_usagePage,

    AdminListPreferencePage,
    AdminAddPreferencePage,
    AdminEditPreferencePage,
    AdminViewPreferencePage,

    AdminListTokensPage,
    AdminAddTokensPage,
    AdminEditTokensPage,
    AdminViewTokensPage,

    AdminListUploadsPage,
    AdminAddUploadsPage,
    AdminEditUploadsPage,
    AdminViewUploadsPage,

    AdminListCmsPage,
    AdminAddCmsPage,
    AdminEditCmsPage,
    AdminViewCmsPage,

    AdminListJobPage,
    AdminAddJobPage,
    AdminEditJobPage,
    AdminViewJobPage
} from "./LazyLoad";


<Route
  exact
  path="/admin/company"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/company"
      element={
        <AdminWrapper>
          <AdminCompanyListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-company"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-company"
      element={
        <AdminWrapper>
          <AdminAddCompanyPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-company/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-company/:id"
      element={
        <AdminWrapper>
          <AdminEditCompanyPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-company/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-company/:id"
      element={
        <AdminWrapper>
          <AdminViewCompanyPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/user"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/user"
      element={
        <AdminWrapper>
          <AdminUserListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-user"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-user"
      element={
        <AdminWrapper>
          <AdminAddUserPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-user/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-user/:id"
      element={
        <AdminWrapper>
          <AdminEditUserPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-user/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-user/:id"
      element={
        <AdminWrapper>
          <AdminViewUserPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/company_user"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/company_user"
      element={
        <AdminWrapper>
          <AdminCompany_userListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-company_user"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-company_user"
      element={
        <AdminWrapper>
          <AdminAddCompany_userPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-company_user/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-company_user/:id"
      element={
        <AdminWrapper>
          <AdminEditCompany_userPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-company_user/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-company_user/:id"
      element={
        <AdminWrapper>
          <AdminViewCompany_userPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/company_admin"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/company_admin"
      element={
        <AdminWrapper>
          <AdminCompany_adminListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-company_admin"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-company_admin"
      element={
        <AdminWrapper>
          <AdminAddCompany_adminPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-company_admin/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-company_admin/:id"
      element={
        <AdminWrapper>
          <AdminEditCompany_adminPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-company_admin/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-company_admin/:id"
      element={
        <AdminWrapper>
          <AdminViewCompany_adminPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/company_employee_subscription"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/company_employee_subscription"
      element={
        <AdminWrapper>
          <AdminCompany_employee_subscriptionListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-company_employee_subscription"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-company_employee_subscription"
      element={
        <AdminWrapper>
          <AdminAddCompany_employee_subscriptionPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-company_employee_subscription/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-company_employee_subscription/:id"
      element={
        <AdminWrapper>
          <AdminEditCompany_employee_subscriptionPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-company_employee_subscription/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-company_employee_subscription/:id"
      element={
        <AdminWrapper>
          <AdminViewCompany_employee_subscriptionPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/company_usage"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/company_usage"
      element={
        <AdminWrapper>
          <AdminCompany_usageListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-company_usage"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-company_usage"
      element={
        <AdminWrapper>
          <AdminAddCompany_usagePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-company_usage/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-company_usage/:id"
      element={
        <AdminWrapper>
          <AdminEditCompany_usagePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-company_usage/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-company_usage/:id"
      element={
        <AdminWrapper>
          <AdminViewCompany_usagePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/preference"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/preference"
      element={
        <AdminWrapper>
          <AdminPreferenceListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-preference"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-preference"
      element={
        <AdminWrapper>
          <AdminAddPreferencePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-preference/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-preference/:id"
      element={
        <AdminWrapper>
          <AdminEditPreferencePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-preference/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-preference/:id"
      element={
        <AdminWrapper>
          <AdminViewPreferencePage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/tokens"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/tokens"
      element={
        <AdminWrapper>
          <AdminTokensListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-tokens"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-tokens"
      element={
        <AdminWrapper>
          <AdminAddTokensPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-tokens/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-tokens/:id"
      element={
        <AdminWrapper>
          <AdminEditTokensPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-tokens/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-tokens/:id"
      element={
        <AdminWrapper>
          <AdminViewTokensPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/uploads"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/uploads"
      element={
        <AdminWrapper>
          <AdminUploadsListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-uploads"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-uploads"
      element={
        <AdminWrapper>
          <AdminAddUploadsPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-uploads/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-uploads/:id"
      element={
        <AdminWrapper>
          <AdminEditUploadsPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-uploads/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-uploads/:id"
      element={
        <AdminWrapper>
          <AdminViewUploadsPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/cms"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/cms"
      element={
        <AdminWrapper>
          <AdminCmsListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-cms"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-cms"
      element={
        <AdminWrapper>
          <AdminAddCmsPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-cms/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-cms/:id"
      element={
        <AdminWrapper>
          <AdminEditCmsPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-cms/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-cms/:id"
      element={
        <AdminWrapper>
          <AdminViewCmsPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/job"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/job"
      element={
        <AdminWrapper>
          <AdminJobListPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/add-job"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/add-job"
      element={
        <AdminWrapper>
          <AdminAddJobPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/edit-job/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/edit-job/:id"
      element={
        <AdminWrapper>
          <AdminEditJobPage />
        </AdminWrapper>
      }
    />
  }
/>

<Route
  exact
  path="/admin/view-job/:id"
  element={
    <PrivateRoute
      access="admin"
      path="/admin/view-job/:id"
      element={
        <AdminWrapper>
          <AdminViewJobPage />
        </AdminWrapper>
      }
    />
  }
/>
    