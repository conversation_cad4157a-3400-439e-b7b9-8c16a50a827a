const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");


const middlewares = [
  TokenMiddleware(),
];

module.exports = function (app) {
  app.get("/v1/api/thinkpartnership/admin/lambda/preference", middlewares, async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.setProjectId("thinkpartnership");

      const Role = require(`../roles/admin`);
      if (!Role.canPreference()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      const result = await sdk.findOne("user", {
        id: req.user_id,
        role_id: req.role,
        status: 1
      });

      if (!result) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      const preferenceResult = await sdk.findOne("preference", {
        user_id: result.id
      });

      return res.status(200).json({
        error: false,
        model: preferenceResult
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });

  app.post("/v1/api/thinkpartnership/admin/lambda/preference", middlewares, async function (req, res) {
    try {
      let sdk = app.get("sdk");
      sdk.setProjectId("thinkpartnership");

      const Role = require(`../roles/admin`);
      if (!Role.canPreference()) {
        return res.status(403).json({
          error: true,
          message: "Forbidden access"
        });
      }

      const result = await sdk.findOne("user", {
        id: req.user_id,
        role_id: req.role,
        status: 1
      });

      if (!result) {
        return res.status(401).json({
          error: true,
          message: "Invalid Credentials",
        });
      }

      const payload = req.body.payload || req.body;
      payload.user_id = result.id;
    
      const preferenceModel = require("../models/preference");
      const preference = new preferenceModel(payload);
      if (!preference.validate()) {
        return res.status(403).json({
          error: true,
          message: "Invalid preference",
          errors: preference.getErrors()
        });
      }


      const pref = await sdk.findOne("preference", {
        user_id: result.id
      });

      if (!pref) {
        await sdk.create("preference", {
          user_id: result.id,
          ...payload
        });
      } else {
        await sdk.update("preference", {
          user_id: pref.user_id,
          id: pref.id
        }, payload);
      }
        

      return res.status(200).json({
        error: false,
        message: "Updated",
      });
    } catch (err) {
      res.status(403);
      res.json({
        error: true,
        message: err.message,
      });
    }
  });
};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "GET",
      name: "Get Preference",
      url: "/v1/api/thinkpartnership/admin/lambda/preference",
      successPayload: '{"error":false, "model": {"id": 1, "user_id": 1, "name": "preference", "value": "value"}}',
      queryBody: [],
      needToken: true,
      errors: [],
    },
    {
      method: "POST",
      name: "Update Preference",
      url: "/v1/api/thinkpartnership/admin/lambda/preference",
      successPayload: '{"error":false, "message": "Updated"}',
      queryBody: [],
      needToken: true,
      errors: [],
    },
  ];
};

