const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const User = require("../models/user");

module.exports = function (app) {
  app.post("/v1/api/thinkpartnership/customer/lambda/login", async function (req, res) {
    try {
      const config = app.get('configuration');
      const sdk = app.get("sdk");
      sdk.setProjectId("thinkpartnership");

      // Check role permissions
      const Role = require(`../roles/customer`);
      if (!Role.canLogin()) {
        return res.status(403).json({
          error: true,
          message: "Customer login is not allowed"
        });
      }

      // Validate required fields
      if (!req.body.email) {
        return res.status(400).json({
          error: true,
          message: "Email is required"
        });
      }
      if (!req.body.password) {
        return res.status(400).json({
          error: true,
          message: "Password is required"
        });
      }

      // Find user
      const user = await sdk.findOne('user', {
        email: req.body.email,
        role_id: Role.slug
      });

      if (!user) {
        return res.status(401).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Verify password
      const validPassword = await PasswordService.compareHash(req.body.password, user.password);
      
      if (!validPassword) {
        return res.status(401).json({
          error: true,
          message: "Invalid credentials"
        });
      }

      // Check account status
      if (user.status == 2) {
        return res.status(403).json({
          error: true,
          message: "Your account is suspended"
        });
      }

      // Get customer profile
      const customer = await sdk.findOne('marketplace_customer', { user_id: user.id });

      // Generate JWT token
      const token = JwtService.createAccessToken(
        {
          user_id: user.id,
          role: Role.slug
        },
        config.access_jwt_expire,
        config.jwt_key
      );

      // Handle refresh token if needed
      let refreshToken;
      if (req.body.is_refresh) {
        refreshToken = JwtService.createAccessToken(
          {
            user_id: user.id,
            role: Role.slug
          },
          config.refresh_jwt_expire,
          config.jwt_key
        );

        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        // Save refresh token to tokens table
        await sdk.create('tokens', {
          user_id: user.id,
          token: refreshToken,
          expired_at: expireDate,
          type: 1,
          created_at: new Date(),
          updated_at: new Date(),
        });
      }

      return res.status(200).json({
        error: false,
        message: 'Login successful',
        token: token,
        refresh_token: refreshToken,
        user_id: user.id,
        customer_id: customer ? customer.id : null,
        role: Role.slug,
        first_name: customer ? customer.first_name : '',
        last_name: customer ? customer.last_name : '',
        expire_at: config.access_jwt_expire
      });

    } catch (err) {
      console.error('Error in Customer Login:', err);
      res.status(500).json({
        error: true,
        message: err.message
      });
    }
  });

  // API definition for Postman collection
  module.exports.getPostmanDefinition = function () {
    return [
      {
        method: "POST",
        name: "Customer Login API",
        url: "/v1/api/thinkpartnership/customer/lambda/login",
        successBody: '{ "email": "<EMAIL>", "password": "password123", "is_refresh": true }',
        successPayload: '{"error":false, "message": "Login successful", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "user_id": 1, "customer_id": 1, "role": "customer"}',
        errors: [
          {
            name: "401",
            body: '{ "email": "<EMAIL>", "password": "wrongpassword" }',
            response: '{"error":true,"message":"Invalid credentials"}',
          },
          {
            name: "400",
            body: '{ "password": "password123" }',
            response: '{"error":true,"message":"Email is required"}',
          },
          {
            name: "403",
            body: '{ "email": "<EMAIL>", "password": "password123" }',
            response: '{"error":true,"message":"Your account is suspended"}',
          }
        ],
        needToken: false,
      },
    ];
  };
};
