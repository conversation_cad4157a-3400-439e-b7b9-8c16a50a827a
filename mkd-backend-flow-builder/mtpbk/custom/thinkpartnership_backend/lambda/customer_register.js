const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const User = require("../models/user");
const MarketplaceCustomer = require("../models/marketplace_customer");

module.exports = function (app) {
  app.post("/v1/api/thinkpartnership/customer/lambda/register", async function (req, res) {
    try {
      const config = app.get('configuration');
      const sdk = app.get("sdk");
      sdk.setProjectId("thinkpartnership");

      // Check role permissions
      const Role = require(`../roles/customer`);
      if (!Role.canRegister()) {
        return res.status(403).json({
          error: true,
          message: "Customer registration is not allowed"
        });
      }

      // Validate required fields
      if (!req.body.email) {
        return res.status(400).json({
          error: true,
          message: "Email is required"
        });
      }
      if (!req.body.password) {
        return res.status(400).json({
          error: true,
          message: "Password is required"
        });
      }
      if (!req.body.first_name) {
        return res.status(400).json({
          error: true,
          message: "First name is required"
        });
      }
      if (!req.body.last_name) {
        return res.status(400).json({
          error: true,
          message: "Last name is required"
        });
      }

      // Check if user already exists
      const existingUser = await sdk.findOne('user', {
        email: req.body.email,
        role_id: Role.slug
      });

      if (existingUser) {
        return res.status(409).json({
          error: true,
          message: "User already exists with this email"
        });
      }

      // Hash password
      const hashedPassword = await PasswordService.hash(req.body.password);

      // Create user record
      const userData = {
        email: req.body.email,
        password: hashedPassword,
        role_id: Role.slug,
        login_type: 0,
        status: 0, // Active
        verify: 1, // Auto-verify for marketplace customers
        company_id: 1, // Default company
        created_at: new Date(),
        updated_at: new Date()
      };

      const userResult = await sdk.create('user', userData);

      if (!userResult || userResult.error) {
        throw new Error('Failed to create user account');
      }

      // Create customer profile
      const customerData = {
        user_id: userResult.id,
        first_name: req.body.first_name,
        last_name: req.body.last_name,
        phone: req.body.phone || null,
        status: 0, // Active
        created_at: new Date(),
        updated_at: new Date()
      };

      const customerResult = await sdk.create('marketplace_customer', customerData);

      if (!customerResult || customerResult.error) {
        // Rollback user creation if customer profile fails
        await sdk.delete('user', { id: userResult.id });
        throw new Error('Failed to create customer profile');
      }

      // Generate JWT token
      const token = JwtService.createAccessToken(
        {
          user_id: userResult.id,
          role: Role.slug
        },
        config.access_jwt_expire,
        config.jwt_key
      );

      return res.status(201).json({
        error: false,
        message: 'Registration successful',
        token: token,
        user_id: userResult.id,
        customer_id: customerResult.id,
        role: Role.slug,
        expire_at: config.access_jwt_expire
      });

    } catch (err) {
      console.error('Error in Customer Registration:', err);
      res.status(500).json({
        error: true,
        message: err.message
      });
    }
  });

  // API definition for Postman collection
  module.exports.getPostmanDefinition = function () {
    return [
      {
        method: "POST",
        name: "Customer Registration API",
        url: "/v1/api/thinkpartnership/customer/lambda/register",
        successBody: '{ "email": "<EMAIL>", "password": "password123", "first_name": "John", "last_name": "Doe", "phone": "************" }',
        successPayload: '{"error":false, "message": "Registration successful", "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...", "user_id": 1, "customer_id": 1, "role": "customer"}',
        errors: [
          {
            name: "409",
            body: '{ "email": "<EMAIL>", "password": "password123", "first_name": "John", "last_name": "Doe" }',
            response: '{"error":true,"message":"User already exists with this email"}',
          },
          {
            name: "400",
            body: '{ "password": "password123", "first_name": "John", "last_name": "Doe" }',
            response: '{"error":true,"message":"Email is required"}',
          },
          {
            name: "400",
            body: '{ "email": "<EMAIL>", "first_name": "John", "last_name": "Doe" }',
            response: '{"error":true,"message":"Password is required"}',
          }
        ],
        needToken: false,
      },
    ];
  };
};
