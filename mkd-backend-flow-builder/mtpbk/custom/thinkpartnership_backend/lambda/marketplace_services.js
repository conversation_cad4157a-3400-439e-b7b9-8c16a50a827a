module.exports = function (app) {
  // Get All Services with Filtering
  app.get("/v1/api/thinkpartnership/marketplace/lambda/services", async function (req, res) {
    try {
      const sdk = app.get("sdk");
      sdk.setProjectId("thinkpartnership");

      // Parse query parameters
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 12;
      const offset = (page - 1) * limit;
      const search = req.query.search || '';
      const category = req.query.category || '';
      const location = req.query.location || '';
      const delivery = req.query.delivery || '';
      const sort = req.query.sort || 'best-match';

      // Build WHERE clause
      let whereConditions = ['s.status = 1']; // Only active services
      let params = [];

      // Search filter
      if (search) {
        whereConditions.push('(s.title LIKE ? OR s.description LIKE ? OR s.short_description LIKE ? OR v.business_name LIKE ?)');
        const searchTerm = `%${search}%`;
        params.push(searchTerm, searchTerm, searchTerm, searchTerm);
      }

      // Category filter
      if (category && category !== 'all') {
        whereConditions.push('c.slug = ?');
        params.push(category);
      }

      // Location filter (province)
      if (location && location !== 'all') {
        whereConditions.push('v.province = ?');
        params.push(location);
      }

      // Delivery time filter
      if (delivery && delivery !== 'all') {
        switch (delivery) {
          case 'same-day':
            whereConditions.push('s.delivery_time LIKE ?');
            params.push('%same day%');
            break;
          case '1-day':
            whereConditions.push('(s.delivery_time LIKE ? OR s.delivery_time LIKE ? OR s.delivery_time LIKE ?)');
            params.push('%same day%', '%1 day%', '%24 hour%');
            break;
          case '3-days':
            whereConditions.push('s.delivery_time NOT LIKE ?');
            params.push('%week%');
            break;
        }
      }

      // Build ORDER BY clause
      let orderBy = 's.created_at DESC'; // Default
      switch (sort) {
        case 'price-low':
          orderBy = 's.base_price ASC';
          break;
        case 'price-high':
          orderBy = 's.base_price DESC';
          break;
        case 'newest':
          orderBy = 's.created_at DESC';
          break;
        case 'popular':
          orderBy = 's.total_orders DESC';
          break;
        case 'rating':
          orderBy = 's.rating DESC';
          break;
      }

      const whereClause = whereConditions.join(' AND ');

      // Get total count
      const countQuery = `
        SELECT COUNT(*) as total
        FROM marketplace_service s
        LEFT JOIN marketplace_vendor v ON s.vendor_id = v.id
        LEFT JOIN service_category c ON s.category_id = c.id
        WHERE ${whereClause}
      `;

      const countResult = await sdk.rawQuery(countQuery, params);
      const total = countResult[0]?.total || 0;

      // Get services with pagination
      const servicesQuery = `
        SELECT 
          s.*,
          v.business_name as vendor_name,
          v.logo_image as vendor_image,
          v.rating as vendor_rating,
          v.completed_orders as vendor_completed_orders,
          v.verified as vendor_verified,
          c.name as category_name,
          c.slug as category_slug
        FROM marketplace_service s
        LEFT JOIN marketplace_vendor v ON s.vendor_id = v.id
        LEFT JOIN service_category c ON s.category_id = c.id
        WHERE ${whereClause}
        ORDER BY ${orderBy}
        LIMIT ? OFFSET ?
      `;

      params.push(limit, offset);
      const services = await sdk.rawQuery(servicesQuery, params);

      // Format response
      const formattedServices = services.map(service => ({
        id: service.id,
        title: service.title,
        slug: service.slug,
        description: service.short_description || service.description,
        price: `$${service.base_price}`,
        base_price: service.base_price,
        vendor: service.vendor_name,
        vendor_image: service.vendor_image,
        vendor_rating: service.vendor_rating,
        vendor_verified: service.vendor_verified,
        completed_orders: service.vendor_completed_orders,
        category: service.category_name,
        category_slug: service.category_slug,
        response_time: service.response_time,
        delivery_time: service.delivery_time,
        rating: service.rating,
        total_reviews: service.total_reviews,
        tags: service.tags ? JSON.parse(service.tags) : [],
        images: service.images ? JSON.parse(service.images) : [],
        image: service.images ? JSON.parse(service.images)[0] : null
      }));

      return res.status(200).json({
        error: false,
        services: formattedServices,
        pagination: {
          page: page,
          limit: limit,
          total: total,
          pages: Math.ceil(total / limit)
        },
        filters: {
          search: search,
          category: category,
          location: location,
          delivery: delivery,
          sort: sort
        }
      });

    } catch (err) {
      console.error('Error in Get All Services:', err);
      res.status(500).json({
        error: true,
        message: err.message
      });
    }
  });

  // Get Service Categories
  app.get("/v1/api/thinkpartnership/marketplace/lambda/categories", async function (req, res) {
    try {
      const sdk = app.get("sdk");
      sdk.setProjectId("thinkpartnership");

      // Get all active categories
      const categories = await sdk.find('service_category', {
        status: 1
      }, {
        sort: { sort_order: 1 }
      });

      return res.status(200).json({
        error: false,
        categories: categories
      });

    } catch (err) {
      console.error('Error in Get Categories:', err);
      res.status(500).json({
        error: true,
        message: err.message
      });
    }
  });

  // API definitions for Postman collection
  module.exports.getPostmanDefinition = function () {
    return [
      {
        method: "GET",
        name: "Get All Services API",
        url: "/v1/api/thinkpartnership/marketplace/lambda/services?page=1&limit=12&search=painting&category=painting&sort=price-low",
        successBody: '',
        successPayload: '{"error":false, "services": [...], "pagination": {"page": 1, "limit": 12, "total": 50, "pages": 5}}',
        errors: [
          {
            name: "500",
            body: '',
            response: '{"error":true,"message":"Internal server error"}',
          }
        ],
        needToken: false,
      },
      {
        method: "GET",
        name: "Get Service Categories API",
        url: "/v1/api/thinkpartnership/marketplace/lambda/categories",
        successBody: '',
        successPayload: '{"error":false, "categories": [{"id": 1, "name": "Painting", "slug": "painting"}, ...]}',
        errors: [
          {
            name: "500",
            body: '',
            response: '{"error":true,"message":"Internal server error"}',
          }
        ],
        needToken: false,
      }
    ];
  };
};
