const TokenMiddleware = require("../../../baas/middleware/TokenMiddleware");

module.exports = function (app) {
  // Create Service Request
  app.post("/v1/api/thinkpartnership/customer/lambda/service-request", [TokenMiddleware()], async function (req, res) {
    try {
      const sdk = app.get("sdk");
      sdk.setProjectId("thinkpartnership");

      // Check role permissions
      const user = req.user;
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }

      const Role = require(`../roles/${req.role}`);
      if (!Role.permissions?.routes?.includes("route_marketplace_service_request")) {
        return res.status(403).json({ error: true, message: 'Access denied' });
      }

      // Validate request body
      if (!req.body.service_id) {
        return res.status(400).json({ error: true, message: 'Service ID is required' });
      }
      if (!req.body.customer_name) {
        return res.status(400).json({ error: true, message: 'Customer name is required' });
      }
      if (!req.body.customer_email) {
        return res.status(400).json({ error: true, message: 'Customer email is required' });
      }
      if (!req.body.customer_phone) {
        return res.status(400).json({ error: true, message: 'Customer phone is required' });
      }
      if (!req.body.service_address) {
        return res.status(400).json({ error: true, message: 'Service address is required' });
      }
      if (!req.body.service_city) {
        return res.status(400).json({ error: true, message: 'Service city is required' });
      }
      if (!req.body.service_postal_code) {
        return res.status(400).json({ error: true, message: 'Service postal code is required' });
      }
      if (!req.body.description) {
        return res.status(400).json({ error: true, message: 'Service description is required' });
      }

      // Get customer profile
      const customer = await sdk.findOne('marketplace_customer', { user_id: req.user_id });

      if (!customer) {
        return res.status(404).json({
          error: true,
          message: 'Customer profile not found'
        });
      }

      // Get service details
      const service = await sdk.findOne('marketplace_service', { id: req.body.service_id });

      if (!service) {
        return res.status(404).json({
          error: true,
          message: 'Service not found'
        });
      }

      // Generate unique request number
      const requestNumber = `REQ-${Date.now()}-${Math.random().toString(36).substr(2, 5).toUpperCase()}`;

      // Create service request
      const requestData = {
        customer_id: customer.id,
        service_id: req.body.service_id,
        vendor_id: service.vendor_id,
        request_number: requestNumber,
        customer_name: req.body.customer_name,
        customer_email: req.body.customer_email,
        customer_phone: req.body.customer_phone,
        service_address: req.body.service_address,
        service_city: req.body.service_city,
        service_postal_code: req.body.service_postal_code,
        preferred_date: req.body.preferred_date || null,
        preferred_time: req.body.preferred_time || null,
        urgency: req.body.urgency || 3, // Default to flexible
        description: req.body.description,
        selected_tier: req.body.selected_tier || null,
        estimated_price: req.body.estimated_price || null,
        attachments: req.body.attachments ? JSON.stringify(req.body.attachments) : null,
        status: 0, // Pending
        created_at: new Date(),
        updated_at: new Date()
      };

      const requestResult = await sdk.create('service_request', requestData);

      if (!requestResult || requestResult.error) {
        throw new Error('Failed to create service request');
      }

      return res.status(201).json({
        error: false,
        message: 'Service request created successfully',
        request: {
          id: requestResult.id,
          request_number: requestNumber,
          status: 'Pending'
        }
      });

    } catch (err) {
      console.error('Error in Create Service Request:', err);
      res.status(500).json({ error: true, message: err.message });
    }
  });

  // Get Customer Service Requests
  app.get("/v1/api/thinkpartnership/customer/lambda/service-requests", [TokenMiddleware()], async function (req, res) {
    try {
      const sdk = app.get("sdk");
      sdk.setProjectId("thinkpartnership");

      // Check authentication
      const user = req.user;
      if (!user || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }

      // Get customer profile
      const customer = await sdk.findOne('marketplace_customer', { user_id: req.user_id });

      if (!customer) {
        return res.status(404).json({
          error: true,
          message: 'Customer profile not found'
        });
      }

      // Get service requests with service and vendor details
      const requestsQuery = `
        SELECT 
          sr.*,
          s.title as service_title,
          s.base_price as service_price,
          v.business_name as vendor_name,
          v.logo_image as vendor_image,
          v.phone as vendor_phone
        FROM service_request sr
        LEFT JOIN marketplace_service s ON sr.service_id = s.id
        LEFT JOIN marketplace_vendor v ON sr.vendor_id = v.id
        WHERE sr.customer_id = ?
        ORDER BY sr.created_at DESC
      `;

      const requests = await sdk.rawQuery(requestsQuery, [customer.id]);

      // Format response
      const formattedRequests = requests.map(request => ({
        id: request.id,
        request_number: request.request_number,
        service_title: request.service_title,
        service_price: request.service_price,
        vendor_name: request.vendor_name,
        vendor_image: request.vendor_image,
        vendor_phone: request.vendor_phone,
        status: request.status,
        urgency: request.urgency,
        preferred_date: request.preferred_date,
        preferred_time: request.preferred_time,
        description: request.description,
        vendor_response: request.vendor_response,
        vendor_quote: request.vendor_quote,
        scheduled_date: request.scheduled_date,
        created_at: request.created_at,
        updated_at: request.updated_at
      }));

      return res.status(200).json({
        error: false,
        requests: formattedRequests
      });

    } catch (err) {
      console.error('Error in Get Customer Service Requests:', err);
      res.status(500).json({ error: true, message: err.message });
    }
  });

  // API definitions for Postman collection
  module.exports.getPostmanDefinition = function () {
    return [
      {
        method: "POST",
        name: "Create Service Request API",
        url: "/v1/api/thinkpartnership/customer/lambda/service-request",
        successBody: '{ "service_id": 1, "customer_name": "John Doe", "customer_email": "<EMAIL>", "customer_phone": "************", "service_address": "123 Main St", "service_city": "Toronto", "service_postal_code": "M5V 3A8", "description": "Need interior painting for 2 bedrooms", "urgency": 2 }',
        successPayload: '{"error":false, "message": "Service request created successfully", "request": {"id": 1, "request_number": "REQ-1234567890-ABC12", "status": "Pending"}}',
        errors: [
          {
            name: "401",
            body: '{}',
            response: '{"error":true,"message":"Authentication required"}',
          },
          {
            name: "400",
            body: '{ "customer_name": "John Doe" }',
            response: '{"error":true,"message":"Service ID is required"}',
          }
        ],
        needToken: true,
      },
      {
        method: "GET",
        name: "Get Service Requests API",
        url: "/v1/api/thinkpartnership/customer/lambda/service-requests",
        successBody: '',
        successPayload: '{"error":false, "requests": [{"id": 1, "request_number": "REQ-1234567890-ABC12", "service_title": "Interior Painting", "status": 0, "created_at": "2024-01-01T00:00:00Z"}]}',
        errors: [
          {
            name: "401",
            body: '',
            response: '{"error":true,"message":"Authentication required"}',
          }
        ],
        needToken: true,
      }
    ];
  };
};
