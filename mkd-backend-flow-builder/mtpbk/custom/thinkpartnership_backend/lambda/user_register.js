const JwtService = require("../../../baas/services/JwtService");
const PasswordService = require("../../../baas/services/PasswordService");
const User = require("../models/user");
const MailService = require("../../../baas/services/MailService");

module.exports = function (app) {
  const config = app.get("configuration");
  app.post("/v1/api/thinkpartnership/user/lambda/register", async function (req, res) {
    try {
      // Get the role class and check registration permission
      const RoleClass = require(`../roles/user`);
      if (!RoleClass.canRegister()) {
        return res.status(403).json({
          error: true,
          message: `${RoleClass.name} cannot register`,
        });
      }

      let verify = RoleClass.canVerifyEmail() ? true : false;
      const needRefreshToken = req.body.is_refresh ? true : false;
      let refreshToken = undefined;

      // Create user object and validate
      const userObj = new User({
        email: req.body.email,
        role_id: RoleClass.slug,
        password: req.body.password,
        login_type: 0,
        status: 0,
        verify: verify ? 0 : 1,
        company_id: req.body.company_id || 0,
        data: JSON.stringify({
          first_name: req.body.first_name || "",
          last_name: req.body.last_name || "",
          photo: req.body.photo || "",
          phone: req.body.phone || ""
        })
      });

      if (!userObj.isValid()) {
        return res.status(403).json({
          error: true,
          message: userObj.getErrors()[0],
          validation: userObj.getErrors().map(error => {
            const [field, message] = error.split(' must ');
            return {
              field: field.replace('Field ', ''),
              message: 'must ' + message
            };
          })
        });
      }

      const sdk = app.get("sdk");

      // Check if user already exists

      sdk.setProjectId("thinkpartnership");
      sdk.setTable("user");
      const existingUser = await sdk.findOne('user', {
          email: userObj.fields.find(f => f.name === 'email').value
      });

      if (existingUser) {
        return res.status(403).json({
          error: true,
          message: "User already exists with this email"
        });
      }

      // Hash password
      // const passwordService = new PasswordService();
      const hashedPassword = await PasswordService.hash(req.body.password);

      // Create user record
      const userData = {
        email: req.body.email,
        password: hashedPassword,
        role_id: RoleClass.slug,
        login_type: 0,
        status: verify ? 0 : 1,
        verify: verify ? 0 : 1,
        company_id: req.body.company_id || 0,
        data: JSON.stringify({
          first_name: req.body.first_name || "",
          last_name: req.body.last_name || "",
          photo: req.body.photo || "",
          phone: req.body.phone || ""
        }),
        created_at: new Date(),
        updated_at: new Date()
      };

      // Insert user

      sdk.setProjectId("thinkpartnership");
      sdk.setTable("user");
      const result = await sdk.create('user', userData);

      if (!result) {
        throw new Error('Failed to create user');
      }

      const userId = result.id;

      // Generate tokens
      const tokenPayload = {
        user_id: userId,
        role: RoleClass.slug
      };

      let response = {
        error: false,
        role: RoleClass.slug,
        token: JwtService.createAccessToken(
          tokenPayload,
          config.access_jwt_expire,
          config.jwt_key
        ),
        expire_at: config.access_jwt_expire,
        user_id: userId
      };

      // Handle refresh token if needed
      if (needRefreshToken) {
        refreshToken = JwtService.createAccessToken(
          tokenPayload,
          config.refresh_jwt_expire,
          config.jwt_key
        );

        let expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        // Save refresh token

        sdk.setProjectId("thinkpartnership");
        sdk.setTable("tokens");
        await sdk.create('tokens', {
          user_id: userId,
          token: refreshToken,
          type: 1,
          data: "{}",
          expired_at: expireDate,
          updated_at: new Date(),
          created_at: new Date()
        });

        response.refresh_token = refreshToken;
      }

      // If verification is required, generate verification token and send email
      if (verify) {
        const verificationToken = JwtService.createAccessToken(
          { user_id: userId, token: Math.random().toString(36).substring(2, 15) },
          config.verification_token_expire,
          config.jwt_key
        );

        await sdk.create('tokens', {
          user_id: userId,
          token: verificationToken,
          type: 3,
          data: '{}',
          expired_at: new Date(Date.now() + config.verification_token_expire * 1000),
          updated_at: new Date(),
          created_at: new Date()
        });

        const mailService = new MailService(config);
        const verificationUrl = `${config.app_url}/verify-email`;
        try {
          await mailService.sendVerificationEmail(
            userData.email,
            verificationToken,
            verificationUrl
          );
        } catch (err) {
          console.error(err);
        }
      }

      return res.status(200).json(response);

    } catch (err) {
      res.status(403).json({
        error: true,
        message: err.message
      });
    }
  });


  return [
    {
      method: "POST",
      name: "Register API",
      url: "/v1/api/thinkpartnership/user/lambda/register",
      successBody: '{ "email": "<EMAIL>", "first_name": "user", "last_name": "User", "is_refresh": true, "password": "a123456"}',
      successPayload: '{"error":false,"role":"member","token":"JWT Token","expire_at":3600,"user_id":20}',
      errors: [
        {
          name: "403",
          body: '{"role": "member", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Email Missing","validation": [{ "field": "email", "message": "Email missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "is_refresh": false}',
          response: '{"error": true,"message": "Password","validation": [{ "field": "password", "message": "Password missing" }]}'
        },
        {
          name: "403",
          body: '{ "email": "<EMAIL>", "role": "member", "password": "a123456", "is_refresh": false}',
          response: '{"error": true,"message": "Some error message"}'
        }
      ],
      needToken: true
    }
  ];

};

// API definition for Postman collection
module.exports.getPostmanDefinition = function () {
  return [
    {
      method: "POST",
      name: "User Registration API",
      url: "/v1/api/thinkpartnership/user/lambda/register",
      successBody: '{ "email": "<EMAIL>", "password": "password123", "name": "New User" }',
      successPayload: '{"error":false, "message": "Registration successful", "user": {"id": 1, "email": "<EMAIL>", "name": "New User"}}',
      errors: [
        {
          name: "409",
          body: '{ "email": "<EMAIL>", "password": "password123", "name": "Existing User" }',
          response: '{"error":true,"message":"Email already exists"}',
        },
        {
          name: "400",
          body: '{}',
          response: '{"error":true,"message":"Invalid registration data","validation":[{"field": "email", "message": "Email is required"}, {"field": "password", "message": "Password is required"}]}',
        }
      ],
      needToken: false,
    },
  ];
};
