const TokenMiddleware = require('../../baas/middleware/TokenMiddleware');

module.exports = function(app) {
  
  // ===== MARKETPLACE AUTHENTICATION APIS =====
  // Custom implementation without SDK for full control
  
  // Customer Registration - Custom implementation
  app.post('/api/marketplace/auth/register', async (req, res) => {
    try {
      // Validate request body
      const { email, password, first_name, last_name, phone } = req.body;
      
      if (!email) {
        return res.status(400).json({ error: true, message: 'Email is required' });
      }
      if (!password) {
        return res.status(400).json({ error: true, message: 'Password is required' });
      }
      if (!first_name) {
        return res.status(400).json({ error: true, message: 'First name is required' });
      }
      if (!last_name) {
        return res.status(400).json({ error: true, message: 'Last name is required' });
      }

      // Email validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        return res.status(400).json({ error: true, message: 'Invalid email format' });
      }

      // Get database connection
      const dbConnector = req.app.get('sdk').db;
      
      // Check if user already exists
      const existingUsers = await dbConnector.find('thinkpartnership_user', { email: email });
      
      if (existingUsers.length > 0) {
        return res.status(400).json({
          error: true,
          message: 'User already exists with this email'
        });
      }

      // Hash password
      const PasswordService = require('../../baas/services/PasswordService');
      const hashedPassword = await PasswordService.hash(password);

      // Create user record
      const userData = {
        email: email,
        password: hashedPassword,
        role_id: 'user', // role_id for marketplace customers
        login_type: 0, // login_type: Regular
        status: 0, // status: Active
        verify: 1, // verify: Auto-verified
        company_id: 0, // company_id: Default
        data: JSON.stringify({
          first_name: first_name || '',
          last_name: last_name || '',
          phone: phone || ''
        }),
        created_at: new Date(),
        updated_at: new Date()
      };

      const userResult = await dbConnector.create('thinkpartnership_user', userData);
      const userId = userResult.id;

      // Generate JWT token
      const JwtService = require('../../baas/services/JwtService');
      const config = req.app.get('configuration');

      const tokenPayload = {
        user_id: userId,
        role: 'user'
      };

      const accessToken = JwtService.createAccessToken(
        tokenPayload,
        config.access_jwt_expire,
        config.jwt_key
      );

      // Generate refresh token if requested
      let refreshToken = null;
      if (req.body.is_refresh) {
        refreshToken = JwtService.createAccessToken(
          tokenPayload,
          config.refresh_jwt_expire,
          config.jwt_key
        );

        // Save refresh token to database
        const expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        const tokenData = {
          user_id: userId,
          token: refreshToken,
          type: 1,
          data: '{}',
          expired_at: expireDate,
          created_at: new Date(),
          updated_at: new Date()
        };

        await dbConnector.create('thinkpartnership_tokens', tokenData);
      }

      // Return success response
      const response = {
        error: false,
        message: 'Registration successful',
        role: 'user',
        token: accessToken,
        expire_at: config.access_jwt_expire,
        user_id: userId
      };

      if (refreshToken) {
        response.refresh_token = refreshToken;
      }

      return res.status(201).json(response);

    } catch (error) {
      console.error('Error in Custom Registration:', error);
      return res.status(500).json({ 
        error: true, 
        message: 'Registration failed: ' + error.message 
      });
    }
  });

  // Customer Login - Custom implementation
  app.post('/api/marketplace/auth/login', async (req, res) => {
    try {
      // Validate request body
      const { email, password } = req.body;
      
      if (!email) {
        return res.status(400).json({ error: true, message: 'Email is required' });
      }
      if (!password) {
        return res.status(400).json({ error: true, message: 'Password is required' });
      }

      // Get database connection
      const dbConnector = req.app.get('sdk').db;
      
      // Find user by email
      const users = await dbConnector.find('thinkpartnership_user', { email: email });
      
      if (users.length === 0) {
        return res.status(401).json({
          error: true,
          message: 'Invalid credentials'
        });
      }

      const user = users[0];

      // Check account status
      if (user.status == 2) {
        return res.status(403).json({
          error: true,
          message: 'Your account is suspended'
        });
      }

      // Verify password
      const PasswordService = require('../../baas/services/PasswordService');
      const validPassword = await PasswordService.compareHash(password, user.password);

      if (!validPassword) {
        return res.status(401).json({
          error: true,
          message: 'Invalid credentials'
        });
      }

      // Parse user data
      let userData = {};
      try {
        userData = JSON.parse(user.data || '{}');
      } catch (e) {
        userData = {};
      }

      // Generate JWT token
      const JwtService = require('../../baas/services/JwtService');
      const config = req.app.get('configuration');

      const tokenPayload = {
        user_id: user.id,
        role: user.role_id
      };

      const accessToken = JwtService.createAccessToken(
        tokenPayload,
        config.access_jwt_expire,
        config.jwt_key
      );

      // Generate refresh token if requested
      let refreshToken = null;
      if (req.body.is_refresh) {
        refreshToken = JwtService.createAccessToken(
          tokenPayload,
          config.refresh_jwt_expire,
          config.jwt_key
        );

        // Save refresh token to database
        const expireDate = new Date();
        expireDate.setSeconds(expireDate.getSeconds() + config.refresh_jwt_expire);

        const tokenData = {
          user_id: user.id,
          token: refreshToken,
          type: 1,
          data: '{}',
          expired_at: expireDate,
          created_at: new Date(),
          updated_at: new Date()
        };

        await dbConnector.create('thinkpartnership_tokens', tokenData);
      }

      // Return success response
      const response = {
        error: false,
        message: 'Login successful',
        role: user.role_id,
        token: accessToken,
        expire_at: config.access_jwt_expire,
        user_id: user.id,
        first_name: userData.first_name || '',
        last_name: userData.last_name || '',
        email: user.email
      };

      if (refreshToken) {
        response.refresh_token = refreshToken;
      }

      return res.status(200).json(response);

    } catch (error) {
      console.error('Error in Custom Login:', error);
      return res.status(500).json({ 
        error: true, 
        message: 'Login failed: ' + error.message 
      });
    }
  });

  // Customer Profile - Custom implementation
  app.get('/api/marketplace/auth/profile', [TokenMiddleware()], async (req, res) => {
    try {
      // Get user ID from token middleware (TokenMiddleware sets req.user_id, not req.user)
      const userId = req.user_id;
      
      if (!userId) {
        return res.status(401).json({
          error: true,
          message: 'Unauthorized'
        });
      }

      // Get database connection
      const dbConnector = req.app.get('sdk').db;
      
      // Get full user details
      const users = await dbConnector.find('thinkpartnership_user', { id: userId });
      
      if (users.length === 0) {
        return res.status(404).json({
          error: true,
          message: 'User not found'
        });
      }

      const userRecord = users[0];

      // Parse user data
      let userData = {};
      try {
        userData = JSON.parse(userRecord.data || '{}');
      } catch (e) {
        userData = {};
      }

      // Return user profile
      return res.status(200).json({
        error: false,
        user: {
          id: userRecord.id,
          email: userRecord.email,
          role: userRecord.role_id,
          status: userRecord.status,
          first_name: userData.first_name || '',
          last_name: userData.last_name || '',
          phone: userData.phone || '',
          photo: userData.photo || '',
          created_at: userRecord.created_at
        }
      });

    } catch (error) {
      console.error('Error in Custom Profile:', error);
      return res.status(500).json({ 
        error: true, 
        message: 'Failed to get profile: ' + error.message 
      });
    }
  });

  // ===== MARKETPLACE CATALOG APIS =====

  // Get Service Categories - Custom implementation
  app.get('/api/marketplace/categories', async (req, res) => {
    try {
      // Get database connection
      const dbConnector = req.app.get('sdk').db;

      // Get all active categories with direct database query
      const categories = await dbConnector.find('thinkpartnership_service_category',
        { status: 1 },
        { sort: { sort_order: 1 } }
      );

      return res.status(200).json({
        error: false,
        categories: categories
      });

    } catch (error) {
      console.error('Error in Get Categories:', error);
      return res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get Services - Custom implementation
  app.get('/api/marketplace/services', async (req, res) => {
    try {
      // Get database connection
      const dbConnector = req.app.get('sdk').db;

      // Build query filters
      const filters = {};
      if (req.query.category_id) {
        filters.category_id = parseInt(req.query.category_id);
      }
      if (req.query.status !== undefined) {
        filters.status = parseInt(req.query.status);
      } else {
        filters.status = 1; // Default to active services
      }

      // Get pagination parameters
      const page = parseInt(req.query.page) || 1;
      const limit = parseInt(req.query.limit) || 10;
      const offset = (page - 1) * limit;

      // Get services with pagination
      const services = await dbConnector.find('thinkpartnership_marketplace_service',
        filters,
        {
          sort: { created_at: -1 },
          limit: limit,
          offset: offset
        }
      );

      // Get total count for pagination
      const totalCount = await dbConnector.count('thinkpartnership_marketplace_service', filters);

      return res.status(200).json({
        error: false,
        services: services,
        pagination: {
          page: page,
          limit: limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit)
        }
      });

    } catch (error) {
      console.error('Error in Get Services:', error);
      return res.status(500).json({ error: true, message: error.message });
    }
  });

  // ===== SERVICE REQUEST APIS =====

  // Create Service Request - Custom implementation
  app.post('/api/marketplace/service-requests', [TokenMiddleware()], async (req, res) => {
    try {
      const userId = req.user_id;

      if (!userId || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }

      // Validate request body
      const { service_id, description, preferred_date, budget_range, location } = req.body;

      if (!service_id) {
        return res.status(400).json({ error: true, message: 'Service ID is required' });
      }
      if (!description) {
        return res.status(400).json({ error: true, message: 'Description is required' });
      }

      // Get database connection
      const dbConnector = req.app.get('sdk').db;

      // Generate unique request number
      const requestNumber = `REQ-${Date.now()}-${Math.random().toString(36).substring(2, 5).toUpperCase()}`;

      // Create service request
      const requestData = {
        user_id: userId,
        service_id: service_id,
        request_number: requestNumber,
        description: description,
        preferred_date: preferred_date || null,
        budget_range: budget_range || null,
        location: location || null,
        status: 0, // Pending
        urgency: req.body.urgency || 'normal',
        created_at: new Date(),
        updated_at: new Date()
      };

      const result = await dbConnector.create('thinkpartnership_service_request', requestData);

      return res.status(201).json({
        error: false,
        message: 'Service request created successfully',
        request: {
          id: result.id,
          request_number: requestNumber,
          status: 'pending'
        }
      });

    } catch (error) {
      console.error('Error in Create Service Request:', error);
      return res.status(500).json({ error: true, message: error.message });
    }
  });

  // Get Customer Service Requests - Custom implementation
  app.get('/api/marketplace/service-requests', [TokenMiddleware()], async (req, res) => {
    try {
      const userId = req.user_id;

      if (!userId || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }

      // Get database connection
      const dbConnector = req.app.get('sdk').db;

      // Get service requests for this user
      const requests = await dbConnector.find('thinkpartnership_service_request',
        { user_id: userId },
        { sort: { created_at: -1 } }
      );

      return res.status(200).json({
        error: false,
        requests: requests
      });

    } catch (error) {
      console.error('Error in Get Customer Service Requests:', error);
      return res.status(500).json({ error: true, message: error.message });
    }
  });

  // ===== ORDER MANAGEMENT APIS =====

  // Get Customer Orders - Custom implementation
  app.get('/api/marketplace/orders', [TokenMiddleware()], async (req, res) => {
    try {
      const userId = req.user_id;

      if (!userId || !req.role) {
        return res.status(401).json({ error: true, message: 'Authentication required' });
      }

      // Get database connection
      const dbConnector = req.app.get('sdk').db;

      // Get orders for this user
      const orders = await dbConnector.find('thinkpartnership_marketplace_order',
        { customer_user_id: userId },
        { sort: { created_at: -1 } }
      );

      return res.status(200).json({
        error: false,
        orders: orders
      });

    } catch (error) {
      console.error('Error in Get Customer Orders:', error);
      return res.status(500).json({ error: true, message: error.message });
    }
  });

};
