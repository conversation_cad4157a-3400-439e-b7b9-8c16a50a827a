
const BaseModel = require('../../../baas/core/BaseModel');

class company extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "name",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "website",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "logo",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "owner_id",
        "type": "integer",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "address",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "city",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "state",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "zip",
        "type": "integer",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "country",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "phone",
        "type": "string",
        "validation": "phone",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": [],
        "defaultValue": "0",
        "mapping": "0:Active,1:Inactive,2:Suspend"
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }


  transformStatus(value) {
    const mappings = {
      '0': 'Active',
      '1': 'Inactive',
      '2': 'Suspend'
    };
    return mappings[value] || value;
  }

  static mapping () {
    return {
      "status": {
        "0": "Active",
        "1": "Inactive",
        "2": "Suspend"
      }
    };
  }

}

module.exports = company;
