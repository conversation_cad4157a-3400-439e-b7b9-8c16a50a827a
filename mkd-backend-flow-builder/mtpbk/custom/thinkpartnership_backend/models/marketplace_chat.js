const BaseModel = require('../../../baas/core/BaseModel');

class marketplace_chat extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "customer_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "vendor_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_id",
        "type": "foreign key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_request_id",
        "type": "foreign key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "subject",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "last_message",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "last_message_sender",
        "type": "mapping",
        "validation": "required,enum:0,1",
        "defaultValue": "0",
        "mapping": "0:Customer,1:Vendor"
      },
      {
        "name": "last_message_date",
        "type": "datetime",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "unread_customer",
        "type": "integer",
        "validation": [],
        "defaultValue": "0",
        "mapping": null
      },
      {
        "name": "unread_vendor",
        "type": "integer",
        "validation": [],
        "defaultValue": "0",
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": "required,enum:0,1,2,3",
        "defaultValue": "0",
        "mapping": "0:Active,1:Closed,2:Archived,3:Blocked"
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      },
      {
        "name": "updated_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformLastMessageSender(value) {
    const mappings = {
      '0': 'Customer',
      '1': 'Vendor'
    };
    return mappings[value] || value;
  }

  transformStatus(value) {
    const mappings = {
      '0': 'Active',
      '1': 'Closed',
      '2': 'Archived',
      '3': 'Blocked'
    };
    return mappings[value] || value;
  }

  static mapping() {
    return {
      "last_message_sender": {
        "0": "Customer",
        "1": "Vendor"
      },
      "status": {
        "0": "Active",
        "1": "Closed",
        "2": "Archived",
        "3": "Blocked"
      }
    };
  }
}

module.exports = marketplace_chat;
