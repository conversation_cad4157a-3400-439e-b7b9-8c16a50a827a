const BaseModel = require('../../../baas/core/BaseModel');

class marketplace_customer extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "user_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "first_name",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "last_name",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "phone",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "address",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "city",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "province",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "postal_code",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "profile_image",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "preferences",
        "type": "json",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": "required,enum:0,1,2",
        "defaultValue": "0",
        "mapping": "0:Active,1:Inactive,2:Suspended"
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      },
      {
        "name": "updated_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformStatus(value) {
    const mappings = {
      '0': 'Active',
      '1': 'Inactive',
      '2': 'Suspended'
    };
    return mappings[value] || value;
  }

  static mapping() {
    return {
      "status": {
        "0": "Active",
        "1": "Inactive",
        "2": "Suspended"
      }
    };
  }
}

module.exports = marketplace_customer;
