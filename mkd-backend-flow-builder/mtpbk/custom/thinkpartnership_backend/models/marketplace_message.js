const BaseModel = require('../../../baas/core/BaseModel');

class marketplace_message extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "chat_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "sender_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "sender_type",
        "type": "mapping",
        "validation": "required,enum:0,1",
        "defaultValue": null,
        "mapping": "0:Customer,1:Vendor"
      },
      {
        "name": "message",
        "type": "text",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "message_type",
        "type": "mapping",
        "validation": "required,enum:0,1,2,3",
        "defaultValue": "0",
        "mapping": "0:Text,1:Image,2:File,3:Quote"
      },
      {
        "name": "attachments",
        "type": "json",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "quote_amount",
        "type": "decimal",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "quote_details",
        "type": "json",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "read_by_customer",
        "type": "boolean",
        "validation": [],
        "defaultValue": "0",
        "mapping": null
      },
      {
        "name": "read_by_vendor",
        "type": "boolean",
        "validation": [],
        "defaultValue": "0",
        "mapping": null
      },
      {
        "name": "read_at_customer",
        "type": "datetime",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "read_at_vendor",
        "type": "datetime",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformSenderType(value) {
    const mappings = {
      '0': 'Customer',
      '1': 'Vendor'
    };
    return mappings[value] || value;
  }

  transformMessageType(value) {
    const mappings = {
      '0': 'Text',
      '1': 'Image',
      '2': 'File',
      '3': 'Quote'
    };
    return mappings[value] || value;
  }

  static mapping() {
    return {
      "sender_type": {
        "0": "Customer",
        "1": "Vendor"
      },
      "message_type": {
        "0": "Text",
        "1": "Image",
        "2": "File",
        "3": "Quote"
      }
    };
  }
}

module.exports = marketplace_message;
