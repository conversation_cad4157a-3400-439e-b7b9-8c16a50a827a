const BaseModel = require('../../../baas/core/BaseModel');

class marketplace_order extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "order_number",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "customer_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "vendor_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_request_id",
        "type": "foreign key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_title",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "selected_tier",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "subtotal",
        "type": "decimal",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "tax_amount",
        "type": "decimal",
        "validation": [],
        "defaultValue": "0.00",
        "mapping": null
      },
      {
        "name": "total_amount",
        "type": "decimal",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "currency",
        "type": "string",
        "validation": "required",
        "defaultValue": "CAD",
        "mapping": null
      },
      {
        "name": "payment_status",
        "type": "mapping",
        "validation": "required,enum:0,1,2,3,4",
        "defaultValue": "0",
        "mapping": "0:Pending,1:Paid,2:Failed,3:Refunded,4:Cancelled"
      },
      {
        "name": "payment_method",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "payment_transaction_id",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "order_status",
        "type": "mapping",
        "validation": "required,enum:0,1,2,3,4,5",
        "defaultValue": "0",
        "mapping": "0:Pending,1:Confirmed,2:In Progress,3:Completed,4:Cancelled,5:Refunded"
      },
      {
        "name": "service_date",
        "type": "datetime",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "completion_date",
        "type": "datetime",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "customer_notes",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "vendor_notes",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "admin_notes",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      },
      {
        "name": "updated_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformPaymentStatus(value) {
    const mappings = {
      '0': 'Pending',
      '1': 'Paid',
      '2': 'Failed',
      '3': 'Refunded',
      '4': 'Cancelled'
    };
    return mappings[value] || value;
  }

  transformOrderStatus(value) {
    const mappings = {
      '0': 'Pending',
      '1': 'Confirmed',
      '2': 'In Progress',
      '3': 'Completed',
      '4': 'Cancelled',
      '5': 'Refunded'
    };
    return mappings[value] || value;
  }

  static mapping() {
    return {
      "payment_status": {
        "0": "Pending",
        "1": "Paid",
        "2": "Failed",
        "3": "Refunded",
        "4": "Cancelled"
      },
      "order_status": {
        "0": "Pending",
        "1": "Confirmed",
        "2": "In Progress",
        "3": "Completed",
        "4": "Cancelled",
        "5": "Refunded"
      }
    };
  }
}

module.exports = marketplace_order;
