const BaseModel = require('../../../baas/core/BaseModel');

class marketplace_review extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "order_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "customer_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "vendor_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "rating",
        "type": "integer",
        "validation": "required,min:1,max:5",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "title",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "comment",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "pros",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "cons",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "would_recommend",
        "type": "boolean",
        "validation": [],
        "defaultValue": "1",
        "mapping": null
      },
      {
        "name": "images",
        "type": "json",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "vendor_response",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "vendor_response_date",
        "type": "datetime",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "helpful_votes",
        "type": "integer",
        "validation": [],
        "defaultValue": "0",
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": "required,enum:0,1,2,3",
        "defaultValue": "0",
        "mapping": "0:Pending,1:Approved,2:Rejected,3:Hidden"
      },
      {
        "name": "moderation_notes",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      },
      {
        "name": "updated_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformStatus(value) {
    const mappings = {
      '0': 'Pending',
      '1': 'Approved',
      '2': 'Rejected',
      '3': 'Hidden'
    };
    return mappings[value] || value;
  }

  static mapping() {
    return {
      "status": {
        "0": "Pending",
        "1": "Approved",
        "2": "Rejected",
        "3": "Hidden"
      }
    };
  }
}

module.exports = marketplace_review;
