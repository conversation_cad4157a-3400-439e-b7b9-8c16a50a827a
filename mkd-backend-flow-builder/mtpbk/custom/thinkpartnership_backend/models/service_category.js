const BaseModel = require('../../../baas/core/BaseModel');

class service_category extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "name",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "slug",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "description",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "icon",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "image",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "parent_id",
        "type": "foreign key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "sort_order",
        "type": "integer",
        "validation": [],
        "defaultValue": "0",
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": "required,enum:0,1",
        "defaultValue": "1",
        "mapping": "0:Inactive,1:Active"
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      },
      {
        "name": "updated_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformStatus(value) {
    const mappings = {
      '0': 'Inactive',
      '1': 'Active'
    };
    return mappings[value] || value;
  }

  static mapping() {
    return {
      "status": {
        "0": "Inactive",
        "1": "Active"
      }
    };
  }
}

module.exports = service_category;
