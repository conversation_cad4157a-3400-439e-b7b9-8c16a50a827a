const BaseModel = require('../../../baas/core/BaseModel');

class service_request extends BaseModel {
  static schema() {
    return [
      {
        "name": "id",
        "type": "primary key",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "customer_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "vendor_id",
        "type": "foreign key",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "request_number",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "customer_name",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "customer_email",
        "type": "string",
        "validation": "required,email",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "customer_phone",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_address",
        "type": "text",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_city",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "service_postal_code",
        "type": "string",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "preferred_date",
        "type": "date",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "preferred_time",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "urgency",
        "type": "mapping",
        "validation": "required,enum:0,1,2,3",
        "defaultValue": "3",
        "mapping": "0:Emergency,1:Urgent,2:Soon,3:Flexible"
      },
      {
        "name": "description",
        "type": "text",
        "validation": "required",
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "selected_tier",
        "type": "string",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "estimated_price",
        "type": "decimal",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "attachments",
        "type": "json",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "status",
        "type": "mapping",
        "validation": "required,enum:0,1,2,3,4,5,6",
        "defaultValue": "0",
        "mapping": "0:Pending,1:Accepted,2:In Progress,3:Completed,4:Cancelled,5:Rejected,6:Expired"
      },
      {
        "name": "vendor_response",
        "type": "text",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "vendor_quote",
        "type": "decimal",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "scheduled_date",
        "type": "datetime",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "completed_date",
        "type": "datetime",
        "validation": [],
        "defaultValue": null,
        "mapping": null
      },
      {
        "name": "created_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP",
        "mapping": null
      },
      {
        "name": "updated_at",
        "type": "timestamp",
        "validation": "date",
        "defaultValue": "CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP",
        "mapping": null
      }
    ];
  }

  transformUrgency(value) {
    const mappings = {
      '0': 'Emergency',
      '1': 'Urgent',
      '2': 'Soon',
      '3': 'Flexible'
    };
    return mappings[value] || value;
  }

  transformStatus(value) {
    const mappings = {
      '0': 'Pending',
      '1': 'Accepted',
      '2': 'In Progress',
      '3': 'Completed',
      '4': 'Cancelled',
      '5': 'Rejected',
      '6': 'Expired'
    };
    return mappings[value] || value;
  }

  static mapping() {
    return {
      "urgency": {
        "0": "Emergency",
        "1": "Urgent",
        "2": "Soon",
        "3": "Flexible"
      },
      "status": {
        "0": "Pending",
        "1": "Accepted",
        "2": "In Progress",
        "3": "Completed",
        "4": "Cancelled",
        "5": "Rejected",
        "6": "Expired"
      }
    };
  }
}

module.exports = service_request;
