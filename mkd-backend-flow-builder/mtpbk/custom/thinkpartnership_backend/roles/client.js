
const BaseRole = require('../../../baas/core/BaseRole');

class client extends BaseRole {
  static id = 'role_1750871645257_p3uhrobea';
  static name = 'client';
  static slug = 'client';
  static permissions = {
    "routes": [],
    "canCreateUsers": true,
    "canEditUsers": true,
    "canDeleteUsers": true,
    "canManageRoles": true,
    "canLogin": true,
    "canRegister": true,
    "canForgot": false,
    "canReset": false,
    "canGoogleLogin": true,
    "canAppleLogin": true,
    "canMicrosoftLogin": true,
    "canMagicLinkLogin": false,
    "canTwitterLogin": false,
    "needs2FA": true,
    "canSetPermissions": true,
    "canPreference": true,
    "canVerifyEmail": true,
    "canUpload": true,
    "canStripe": true,
    "canStripeWebhook": true,
    "canRealTime": true,
    "canAI": true,
    "canUpdateEmail": true,
    "canUpdatePassword": true,
    "canUpdateOtherUsers": true,
    "treeql": {
      "enabled": true,
      "models": {
        "company": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "user": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "company_user": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "company_admin": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "company_employee_subscription": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "company_usage": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "preference": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "tokens": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "uploads": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "cms": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        },
        "job": {
          "allowed": true,
          "blacklistedFields": [],
          "operations": {
            "get": true,
            "getOne": true,
            "getAll": true,
            "post": true,
            "put": true,
            "delete": true,
            "paginate": true,
            "join": true
          }
        }
      }
    }
  };
  static index = 1;

  // Helper method to check route permission
  static hasRoutePermission(routeId) {
    return this.permissions?.routes?.includes(routeId) || false;
  }

  // List of models this role can access
  static allowedModels = [
    "company",
    "user",
    "company_user",
    "company_admin",
    "company_employee_subscription",
    "company_usage",
    "preference",
    "tokens",
    "uploads",
    "cms",
    "job"
  ];

  /**
  * Check if role can access a specific model
  * @param {string} modelName - Name of the model to check
  * @returns {boolean} Whether model access is allowed
  */
  static canAccessModel(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.allowed || false;
  }

  /**
  * Get blacklisted fields for a model
  * @param {string} modelName - Name of the model
  * @returns {string[]} Array of blacklisted field names
  */
  static getBlacklistedFields(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.blacklistedFields || [];
  }

  /**
  * Check if role can perform an operation on a model
  * @param {string} modelName - Name of the model
  * @param {string} operation - Operation to check (get, getOne, getAll, post, put, delete, paginate, join)
  * @returns {boolean} Whether operation is allowed
  */
  static canPerformOperation(modelName, operation) {
    const modelConfig = this.permissions?.treeql?.models?.[modelName];
    if (!modelConfig?.allowed) {
      return false;
    }
    return modelConfig.operations?.[operation] || false;
  }

  /**
  * Get all allowed operations for a model
  * @param {string} modelName - Name of the model
  * @returns {Object<string, boolean>} Object mapping operations to permission status
  */
  static getAllowedOperations(modelName) {
    return this.permissions?.treeql?.models?.[modelName]?.operations || {};
  }

  /**
  * Check if TreeQL is enabled for this role
  * @returns {boolean} Whether TreeQL is enabled
  */
  static isTreeQLEnabled() {
    return this.permissions?.treeql?.enabled || false;
  }
}

module.exports = client;
