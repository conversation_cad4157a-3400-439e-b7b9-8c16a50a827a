const BaseRole = require('../../../baas/core/BaseRole');

class customer extends BaseRole {
  static id = 'role_marketplace_customer';
  static name = 'customer';
  static slug = 'customer';
  static permissions = {
    "routes": [
      "route_marketplace_services_list",
      "route_marketplace_services_search",
      "route_marketplace_service_details",
      "route_marketplace_service_request",
      "route_marketplace_orders_list",
      "route_marketplace_orders_details",
      "route_marketplace_chat_list",
      "route_marketplace_chat_messages",
      "route_marketplace_chat_send",
      "route_marketplace_reviews_create",
      "route_marketplace_customer_profile",
      "route_marketplace_customer_update"
    ],
    "canCreateUsers": false,
    "canEditUsers": false,
    "canDeleteUsers": false,
    "canManageRoles": false,
    "canLogin": true,
    "canRegister": true,
    "canForgot": true,
    "canReset": true,
    "canGoogleLogin": true,
    "canAppleLogin": true,
    "canMicrosoftLogin": true,
    "canMagicLinkLogin": false,
    "canTwitterLogin": false,
    "needs2FA": false,
    "canSetPermissions": false,
    "canPreference": true,
    "canVerifyEmail": true,
    "canUpload": true,
    "canStripe": false,
    "canStripeWebhook": false,
    "canRealTime": true,
    "canAI": false,
    "canUpdateEmail": true,
    "canUpdatePassword": true,
    "canUpdateOtherUsers": false,
    "treeql": {
      "enabled": true,
      "models": {
        "marketplace_service": {
          "allowed": true,
          "operations": {
            "read": true,
            "create": false,
            "update": false,
            "delete": false
          },
          "blacklistedFields": []
        },
        "service_category": {
          "allowed": true,
          "operations": {
            "read": true,
            "create": false,
            "update": false,
            "delete": false
          },
          "blacklistedFields": []
        },
        "marketplace_vendor": {
          "allowed": true,
          "operations": {
            "read": true,
            "create": false,
            "update": false,
            "delete": false
          },
          "blacklistedFields": ["business_license", "insurance_info"]
        },
        "service_request": {
          "allowed": true,
          "operations": {
            "read": true,
            "create": true,
            "update": true,
            "delete": false
          },
          "blacklistedFields": ["vendor_response", "vendor_quote"]
        },
        "marketplace_order": {
          "allowed": true,
          "operations": {
            "read": true,
            "create": false,
            "update": false,
            "delete": false
          },
          "blacklistedFields": ["vendor_notes", "admin_notes"]
        },
        "marketplace_review": {
          "allowed": true,
          "operations": {
            "read": true,
            "create": true,
            "update": true,
            "delete": false
          },
          "blacklistedFields": ["moderation_notes"]
        },
        "marketplace_chat": {
          "allowed": true,
          "operations": {
            "read": true,
            "create": true,
            "update": true,
            "delete": false
          },
          "blacklistedFields": []
        },
        "marketplace_message": {
          "allowed": true,
          "operations": {
            "read": true,
            "create": true,
            "update": true,
            "delete": false
          },
          "blacklistedFields": []
        },
        "marketplace_customer": {
          "allowed": true,
          "operations": {
            "read": true,
            "create": true,
            "update": true,
            "delete": false
          },
          "blacklistedFields": []
        }
      }
    }
  };
}

module.exports = customer;
