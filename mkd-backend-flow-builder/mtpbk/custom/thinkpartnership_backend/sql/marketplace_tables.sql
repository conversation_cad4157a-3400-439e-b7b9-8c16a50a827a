-- Marketplace Database Schema
-- Generated for ThinkPartnership Marketplace Portal

-- Table: marketplace_customer
CREATE TABLE marketplace_customer (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    first_name VA<PERSON>HAR(255) NOT NULL,
    last_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    province VARCHAR(100),
    postal_code VARCHAR(10),
    profile_image VARCHAR(500),
    preferences JSON,
    status TINYINT NOT NULL DEFAULT 0 COMMENT '0:Active,1:Inactive,2:Suspended',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    INDEX idx_customer_user_id (user_id),
    INDEX idx_customer_status (status)
);

-- Table: marketplace_vendor
CREATE TABLE marketplace_vendor (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    business_name VARCHAR(255) NOT NULL,
    contact_name VARCHAR(255) NOT NULL,
    phone VARCHAR(20) NOT NULL,
    business_address TEXT,
    city VARCHAR(100),
    province VARCHAR(100),
    postal_code VARCHAR(10),
    business_license VARCHAR(255),
    insurance_info TEXT,
    description TEXT,
    logo_image VARCHAR(500),
    cover_image VARCHAR(500),
    website_url VARCHAR(500),
    service_areas JSON,
    rating DECIMAL(3,2) DEFAULT 0.0,
    total_reviews INT DEFAULT 0,
    completed_orders INT DEFAULT 0,
    verified BOOLEAN DEFAULT FALSE,
    status TINYINT NOT NULL DEFAULT 0 COMMENT '0:Pending,1:Active,2:Inactive,3:Suspended',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES user(id) ON DELETE CASCADE,
    INDEX idx_vendor_user_id (user_id),
    INDEX idx_vendor_status (status),
    INDEX idx_vendor_rating (rating),
    INDEX idx_vendor_verified (verified)
);

-- Table: service_category
CREATE TABLE service_category (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    icon VARCHAR(255),
    image VARCHAR(500),
    parent_id INT,
    sort_order INT DEFAULT 0,
    status TINYINT NOT NULL DEFAULT 1 COMMENT '0:Inactive,1:Active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES service_category(id) ON DELETE SET NULL,
    INDEX idx_category_slug (slug),
    INDEX idx_category_parent (parent_id),
    INDEX idx_category_status (status),
    INDEX idx_category_sort (sort_order)
);

-- Table: marketplace_service
CREATE TABLE marketplace_service (
    id INT AUTO_INCREMENT PRIMARY KEY,
    vendor_id INT NOT NULL,
    category_id INT NOT NULL,
    title VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT NOT NULL,
    short_description TEXT,
    base_price DECIMAL(10,2) NOT NULL,
    pricing_tiers JSON,
    features JSON,
    tags JSON,
    images JSON,
    response_time VARCHAR(100),
    delivery_time VARCHAR(100),
    service_areas JSON,
    requirements TEXT,
    rating DECIMAL(3,2) DEFAULT 0.0,
    total_reviews INT DEFAULT 0,
    total_orders INT DEFAULT 0,
    status TINYINT NOT NULL DEFAULT 0 COMMENT '0:Draft,1:Active,2:Inactive,3:Suspended',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (vendor_id) REFERENCES marketplace_vendor(id) ON DELETE CASCADE,
    FOREIGN KEY (category_id) REFERENCES service_category(id) ON DELETE RESTRICT,
    INDEX idx_service_vendor (vendor_id),
    INDEX idx_service_category (category_id),
    INDEX idx_service_slug (slug),
    INDEX idx_service_status (status),
    INDEX idx_service_rating (rating),
    INDEX idx_service_price (base_price),
    FULLTEXT idx_service_search (title, description, short_description)
);

-- Table: service_request
CREATE TABLE service_request (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    service_id INT NOT NULL,
    vendor_id INT NOT NULL,
    request_number VARCHAR(50) NOT NULL UNIQUE,
    customer_name VARCHAR(255) NOT NULL,
    customer_email VARCHAR(255) NOT NULL,
    customer_phone VARCHAR(20) NOT NULL,
    service_address TEXT NOT NULL,
    service_city VARCHAR(100) NOT NULL,
    service_postal_code VARCHAR(10) NOT NULL,
    preferred_date DATE,
    preferred_time VARCHAR(50),
    urgency TINYINT NOT NULL DEFAULT 3 COMMENT '0:Emergency,1:Urgent,2:Soon,3:Flexible',
    description TEXT NOT NULL,
    selected_tier VARCHAR(255),
    estimated_price DECIMAL(10,2),
    attachments JSON,
    status TINYINT NOT NULL DEFAULT 0 COMMENT '0:Pending,1:Accepted,2:In Progress,3:Completed,4:Cancelled,5:Rejected,6:Expired',
    vendor_response TEXT,
    vendor_quote DECIMAL(10,2),
    scheduled_date DATETIME,
    completed_date DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES marketplace_customer(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES marketplace_service(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES marketplace_vendor(id) ON DELETE CASCADE,
    INDEX idx_request_customer (customer_id),
    INDEX idx_request_vendor (vendor_id),
    INDEX idx_request_service (service_id),
    INDEX idx_request_number (request_number),
    INDEX idx_request_status (status),
    INDEX idx_request_urgency (urgency),
    INDEX idx_request_date (preferred_date)
);

-- Table: marketplace_order
CREATE TABLE marketplace_order (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_number VARCHAR(50) NOT NULL UNIQUE,
    customer_id INT NOT NULL,
    vendor_id INT NOT NULL,
    service_id INT NOT NULL,
    service_request_id INT,
    service_title VARCHAR(255) NOT NULL,
    selected_tier VARCHAR(255),
    subtotal DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) DEFAULT 0.00,
    total_amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'CAD',
    payment_status TINYINT NOT NULL DEFAULT 0 COMMENT '0:Pending,1:Paid,2:Failed,3:Refunded,4:Cancelled',
    payment_method VARCHAR(100),
    payment_transaction_id VARCHAR(255),
    order_status TINYINT NOT NULL DEFAULT 0 COMMENT '0:Pending,1:Confirmed,2:In Progress,3:Completed,4:Cancelled,5:Refunded',
    service_date DATETIME,
    completion_date DATETIME,
    customer_notes TEXT,
    vendor_notes TEXT,
    admin_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES marketplace_customer(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES marketplace_vendor(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES marketplace_service(id) ON DELETE CASCADE,
    FOREIGN KEY (service_request_id) REFERENCES service_request(id) ON DELETE SET NULL,
    INDEX idx_order_customer (customer_id),
    INDEX idx_order_vendor (vendor_id),
    INDEX idx_order_service (service_id),
    INDEX idx_order_number (order_number),
    INDEX idx_order_payment_status (payment_status),
    INDEX idx_order_status (order_status),
    INDEX idx_order_date (created_at)
);

-- Table: marketplace_review
CREATE TABLE marketplace_review (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_id INT NOT NULL,
    customer_id INT NOT NULL,
    vendor_id INT NOT NULL,
    service_id INT NOT NULL,
    rating TINYINT NOT NULL CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    comment TEXT,
    pros TEXT,
    cons TEXT,
    would_recommend BOOLEAN DEFAULT TRUE,
    images JSON,
    vendor_response TEXT,
    vendor_response_date DATETIME,
    helpful_votes INT DEFAULT 0,
    status TINYINT NOT NULL DEFAULT 0 COMMENT '0:Pending,1:Approved,2:Rejected,3:Hidden',
    moderation_notes TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (order_id) REFERENCES marketplace_order(id) ON DELETE CASCADE,
    FOREIGN KEY (customer_id) REFERENCES marketplace_customer(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES marketplace_vendor(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES marketplace_service(id) ON DELETE CASCADE,
    INDEX idx_review_order (order_id),
    INDEX idx_review_customer (customer_id),
    INDEX idx_review_vendor (vendor_id),
    INDEX idx_review_service (service_id),
    INDEX idx_review_rating (rating),
    INDEX idx_review_status (status)
);

-- Table: marketplace_chat
CREATE TABLE marketplace_chat (
    id INT AUTO_INCREMENT PRIMARY KEY,
    customer_id INT NOT NULL,
    vendor_id INT NOT NULL,
    service_id INT,
    service_request_id INT,
    subject VARCHAR(255),
    last_message TEXT,
    last_message_sender TINYINT NOT NULL DEFAULT 0 COMMENT '0:Customer,1:Vendor',
    last_message_date DATETIME,
    unread_customer INT DEFAULT 0,
    unread_vendor INT DEFAULT 0,
    status TINYINT NOT NULL DEFAULT 0 COMMENT '0:Active,1:Closed,2:Archived,3:Blocked',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES marketplace_customer(id) ON DELETE CASCADE,
    FOREIGN KEY (vendor_id) REFERENCES marketplace_vendor(id) ON DELETE CASCADE,
    FOREIGN KEY (service_id) REFERENCES marketplace_service(id) ON DELETE SET NULL,
    FOREIGN KEY (service_request_id) REFERENCES service_request(id) ON DELETE SET NULL,
    INDEX idx_chat_customer (customer_id),
    INDEX idx_chat_vendor (vendor_id),
    INDEX idx_chat_service (service_id),
    INDEX idx_chat_status (status),
    INDEX idx_chat_last_message (last_message_date)
);

-- Table: marketplace_message
CREATE TABLE marketplace_message (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chat_id INT NOT NULL,
    sender_id INT NOT NULL,
    sender_type TINYINT NOT NULL COMMENT '0:Customer,1:Vendor',
    message TEXT NOT NULL,
    message_type TINYINT NOT NULL DEFAULT 0 COMMENT '0:Text,1:Image,2:File,3:Quote',
    attachments JSON,
    quote_amount DECIMAL(10,2),
    quote_details JSON,
    read_by_customer BOOLEAN DEFAULT FALSE,
    read_by_vendor BOOLEAN DEFAULT FALSE,
    read_at_customer DATETIME,
    read_at_vendor DATETIME,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (chat_id) REFERENCES marketplace_chat(id) ON DELETE CASCADE,
    INDEX idx_message_chat (chat_id),
    INDEX idx_message_sender (sender_id, sender_type),
    INDEX idx_message_type (message_type),
    INDEX idx_message_read_customer (read_by_customer),
    INDEX idx_message_read_vendor (read_by_vendor),
    INDEX idx_message_date (created_at)
);

-- Insert default service categories
INSERT INTO service_category (name, slug, description, sort_order, status) VALUES
('Painting', 'painting', 'Interior and exterior painting services', 1, 1),
('Plumbing', 'plumbing', 'Plumbing repairs and installations', 2, 1),
('Electrical', 'electrical', 'Electrical repairs and installations', 3, 1),
('HVAC', 'hvac', 'Heating, ventilation, and air conditioning services', 4, 1),
('Cleaning', 'cleaning', 'House and commercial cleaning services', 5, 1),
('Landscaping', 'landscaping', 'Lawn care and landscaping services', 6, 1),
('Moving', 'moving', 'Moving and relocation services', 7, 1),
('Inspections', 'inspections', 'Home and property inspection services', 8, 1);
