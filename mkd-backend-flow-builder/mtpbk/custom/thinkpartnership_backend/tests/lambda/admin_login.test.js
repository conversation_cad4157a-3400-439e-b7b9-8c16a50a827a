const APITestFramework = require("../../../../tests/apitesting.base.js");
const BASE_URL = "http://localhost:5172";

class LoginApiTests {
  constructor() {
    this.framework = new APITestFramework();
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("admin Login API Tests", () => {
      // Setup before running tests
      this.framework.beforeEach(async () => {
        // Setup code here if needed
      });

      // Successful login test
      this.framework.addTestCase("admin Login - Success", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/thinkpartnership/admin/lambda/login",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              password: "a123456",
              role: "admin",
            }),
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(!response.body.error, "Should not return error");
        this.framework.assert(
          response.body.token,
          "Should return access token"
        );
        this.framework.assert(
          response.body.role === "admin",
          "Should return correct role"
        );
        this.framework.assert(response.body.user_id, "Should return user ID");
        this.framework.assert(
          response.body.expire_at,
          "Should return token expiration"
        );
      });

      // Login with refresh token test
      this.framework.addTestCase("admin Login with Refresh Token", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/thinkpartnership/admin/lambda/login",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              password: "a123456",
              role: "admin",
              is_refresh: true,
            }),
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(!response.body.error, "Should not return error");
        this.framework.assert(
          response.body.token,
          "Should return access token"
        );
        this.framework.assert(
          response.body.refresh_token,
          "Should return refresh token"
        );
        this.framework.assert(
          response.body.role === "admin",
          "Should return correct role"
        );
      });

      // Invalid credentials test
      this.framework.addTestCase(
        "admin Login - Invalid Credentials",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL + "/v1/api/thinkpartnership/admin/lambda/login",
            {
              method: "POST",
              body: JSON.stringify({
                email: "<EMAIL>",
                password: "wrongPassword",
                role: "admin",
              }),
            }
          );

          this.framework.assert(
            response.status === 403,
            "Should return 403 status code"
          );
          this.framework.assert(
            response.body.error === true,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.message === "Invalid credentials",
            "Should return error message"
          );
        }
      );

      // Invalid validation test
      this.framework.addTestCase(
        "admin Login - Invalid Input Validation",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL + "/v1/api/thinkpartnership/admin/lambda/login",
            {
              method: "POST",
              body: JSON.stringify({
                email: "invalid-email",
                password: "",
                role: "admin",
              }),
            }
          );

          this.framework.assert(
            response.status === 400,
            "Should return 400 status code"
          );
          this.framework.assert(
            response.body.error === true,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.message === "Validation failed",
            "Should return validation message"
          );
          this.framework.assert(
            Array.isArray(response.body.validation),
            "Should return validation errors array"
          );
        }
      );

      // Disabled account test
      this.framework.addTestCase("admin Login - Disabled Account", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/thinkpartnership/admin/lambda/login",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              password: "a123456",
              role: "admin",
            }),
          }
        );

        this.framework.assert(
          response.status === 403,
          "Should return 403 status code"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message === "Your account is disabled",
          "Should return disabled account message"
        );
      });

      // Unverified email test
      this.framework.addTestCase("admin Login - Unverified Email", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/thinkpartnership/admin/lambda/login",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              password: "a123456",
              role: "admin",
            }),
          }
        );

        this.framework.assert(
          response.status === 403,
          "Should return 403 status code"
        );
        this.framework.assert(
          response.body.error === true,
          "Should return error flag"
        );
        this.framework.assert(
          response.body.message === "Your email is not verified",
          "Should return unverified email message"
        );
      });

      // Forbidden role access test
      this.framework.addTestCase(
        "admin Login - Forbidden Role Access",
        async () => {
          // Mock Role.canLogin to return false
          const response = await this.framework.makeRequest(
            BASE_URL + "/v1/api/thinkpartnership/admin/lambda/login",
            {
              method: "POST",
              body: JSON.stringify({
                email: "<EMAIL>",
                password: "a123456",
                role: "restricted-role",
              }),
            }
          );

          this.framework.assert(
            response.status === 403,
            "Should return 403 status code"
          );
          this.framework.assert(
            response.body.error === true,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.message === "Forbidden access",
            "Should return forbidden access message"
          );
        }
      );

      // Missing required fields test
      this.framework.addTestCase(
        "admin Login - Missing Required Fields",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL + "/v1/api/thinkpartnership/admin/lambda/login",
            {
              method: "POST",
              body: JSON.stringify({
                email: "<EMAIL>",
                // Missing password and role
              }),
            }
          );

          this.framework.assert(
            response.status === 400,
            "Should return 400 status code"
          );
          this.framework.assert(
            response.body.error === true,
            "Should return error flag"
          );
          this.framework.assert(
            response.body.validation,
            "Should return validation errors"
          );
        }
      );

      // Test successful login with additional user data
      this.framework.addTestCase(
        "admin Login - Success with User Data",
        async () => {
          const response = await this.framework.makeRequest(
            BASE_URL + "/v1/api/thinkpartnership/admin/lambda/login",
            {
              method: "POST",
              body: JSON.stringify({
                email: "<EMAIL>",
                password: "a123456",
                role: "admin",
              }),
            }
          );

          this.framework.assert(
            response.status === 200,
            "Should return 200 status code"
          );
          this.framework.assert(
            !response.body.error,
            "Should not return error"
          );
          this.framework.assert(
            response.body.first_name !== undefined,
            "Should return first name"
          );
          this.framework.assert(
            response.body.last_name !== undefined,
            "Should return last name"
          );
          this.framework.assert(
            response.body.photo !== undefined,
            "Should return photo"
          );
        }
      );

      // Test expired token refresh
      this.framework.addTestCase("admin Login - Token Refresh", async () => {
        const response = await this.framework.makeRequest(
          BASE_URL + "/v1/api/thinkpartnership/admin/lambda/login",
          {
            method: "POST",
            body: JSON.stringify({
              email: "<EMAIL>",
              password: "a123456",
              role: "admin",
              is_refresh: true,
            }),
          }
        );

        this.framework.assert(
          response.status === 200,
          "Should return 200 status code"
        );
        this.framework.assert(!response.body.error, "Should not return error");
        this.framework.assert(
          response.body.token,
          "Should return new access token"
        );
        this.framework.assert(
          response.body.refresh_token,
          "Should return new refresh token"
        );
        this.framework.assert(
          response.body.expire_at > 0,
          "Should return valid expiration time"
        );
      });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      // without generating a report (let the test runner handle that)
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create and run tests
const tests = new LoginApiTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
