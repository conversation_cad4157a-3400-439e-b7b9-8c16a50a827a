const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Upload API Tests
 * Class-based implementation of the Upload API tests
 */
class UploadTests {
  constructor() {
    this.framework = new APITestFramework();

    // Define expected response schemas
    this.uploadResponseSchema = {
      error: false,
      url: "string",
      message: "string",
    };

    this.listFilesSchema = {
      error: false,
      list: "object",
      total: "number",
      limit: "number",
      num_pages: "number",
      page: "number",
    };

    this.setupTests();
  }

  setupTests() {
    this.framework.describe("client Upload API Tests", () => {
      let authToken;

      // Setup before each test
      this.framework.beforeEach(async () => {
        // Setup mock data
        authToken = "Bearer mock_auth_token";
      });

      // Test case for Upload File
      this.framework.addTestCase("client Upload File - Success Path", async () => {
        // Create spy to track request
        const requestSpy = this.framework.createSpy(
          this.framework,
          "makeRequest"
        );

        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/upload`,
          {
            error: false,
            url: "https://storage.example.com/files/test-file.jpg",
            message: "File uploaded successfully",
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        // In a real test, we would use FormData to upload a file
        // For this mock test, we'll simulate the response
        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/upload`,
          {
            method: "POST",
            headers: {
              "Content-Type": "multipart/form-data",
              Authorization: authToken,
            },
            // In a real test, this would be FormData with a file
            body: "mock_form_data_with_file",
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Upload File should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Upload File error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(
          response,
          this.uploadResponseSchema
        );
        this.framework.assertions.assertEquals(
          response.body.message,
          "File uploaded successfully",
          "Should return success message"
        );

        // Verify request was made correctly
        this.framework.assert(
          requestSpy.callCount() === 1,
          "API should be called exactly once"
        );
      });

      // Test case for Upload File - Invalid File Type
      this.framework.addTestCase(
        "client Upload File - Invalid File Type",
        async () => {
          // Mock the API response for error case
          this.framework.mockRequest(
            `${BASE_URL}/v1/api/lambda/upload`,
            {
              error: true,
              message: "Invalid file type",
            },
            {
              status: 400,
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          const response = await this.framework.makeRequest(
            `${BASE_URL}/v1/api/lambda/upload`,
            {
              method: "POST",
              headers: {
                "Content-Type": "multipart/form-data",
                Authorization: authToken,
              },
              // In a real test, this would be FormData with an invalid file
              body: "mock_form_data_with_invalid_file",
            }
          );

          // Assertions for error case
          this.framework.assertions.assertEquals(
            response.status,
            400,
            "Should return 400 status for invalid file type"
          );
          this.framework.assertions.assertEquals(
            response.body.error,
            true,
            "Error flag should be true"
          );
          this.framework.assertions.assertEquals(
            response.body.message,
            "Invalid file type",
            "Should return correct error message"
          );
        }
      );

      // Test case for Upload File - File Too Large
      this.framework.addTestCase(
        "client Upload File - File Too Large",
        async () => {
          // Mock the API response for error case
          this.framework.mockRequest(
            `${BASE_URL}/v1/api/lambda/upload`,
            {
              error: true,
              message: "File size exceeds the maximum limit",
            },
            {
              status: 400,
              headers: {
                "Content-Type": "application/json",
              },
            }
          );

          const response = await this.framework.makeRequest(
            `${BASE_URL}/v1/api/lambda/upload`,
            {
              method: "POST",
              headers: {
                "Content-Type": "multipart/form-data",
                Authorization: authToken,
              },
              // In a real test, this would be FormData with a large file
              body: "mock_form_data_with_large_file",
            }
          );

          // Assertions for error case
          this.framework.assertions.assertEquals(
            response.status,
            400,
            "Should return 400 status for file too large"
          );
          this.framework.assertions.assertEquals(
            response.body.error,
            true,
            "Error flag should be true"
          );
          this.framework.assertions.assertEquals(
            response.body.message,
            "File size exceeds the maximum limit",
            "Should return correct error message"
          );
        }
      );

      // Test case for List Files
      this.framework.addTestCase("client List Files - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/upload/list?limit=10&page=1`,
          {
            error: false,
            list: [
              {
                id: 1,
                filename: "test-file.jpg",
                url: "https://storage.example.com/files/test-file.jpg",
                size: 1024,
                mime_type: "image/jpeg",
                created_at: "2023-01-01T00:00:00.000Z",
              },
            ],
            total: 1,
            limit: 10,
            num_pages: 1,
            page: 1,
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/upload/list?limit=10&page=1`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken,
            },
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "List Files should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "List Files error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertResponseValid(
          response,
          this.listFilesSchema
        );
        this.framework.assertions.assertEquals(
          response.body.total,
          1,
          "Should return correct total count"
        );
      });

      // Test case for Delete File
      this.framework.addTestCase("client Delete File - Success Path", async () => {
        // Mock the API response
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/upload/1`,
          {
            error: false,
            message: "File deleted successfully",
          },
          {
            status: 200,
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/upload/1`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken,
            },
          }
        );

        // Assertions
        this.framework.assert(
          response.status === 200,
          "Delete File should return 200 status"
        );
        this.framework.assert(
          response.body.error === false,
          "Delete File error flag should be false"
        );

        // Enhanced assertions
        this.framework.assertions.assertEquals(
          response.body.message,
          "File deleted successfully",
          "Should return success message"
        );
      });

      // Test case for Delete File - Not Found
      this.framework.addTestCase("client Delete File - Not Found", async () => {
        // Mock the API response for error case
        this.framework.mockRequest(
          `${BASE_URL}/v1/api/lambda/upload/999`,
          {
            error: true,
            message: "File not found",
          },
          {
            status: 404,
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        const response = await this.framework.makeRequest(
          `${BASE_URL}/v1/api/lambda/upload/999`,
          {
            method: "DELETE",
            headers: {
              "Content-Type": "application/json",
              Authorization: authToken,
            },
          }
        );

        // Assertions for error case
        this.framework.assertions.assertEquals(
          response.status,
          404,
          "Should return 404 status for file not found"
        );
        this.framework.assertions.assertEquals(
          response.body.error,
          true,
          "Error flag should be true"
        );
        this.framework.assertions.assertEquals(
          response.body.message,
          "File not found",
          "Should return correct error message"
        );
      });
    });
  }

  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new UploadTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
