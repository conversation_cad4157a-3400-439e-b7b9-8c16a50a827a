const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Refresh Token API Tests
 * Class-based implementation of the Refresh Token API tests
 */
class RefreshTokenTests {
  constructor() {
    this.framework = new APITestFramework();
    this.baseUrl = BASE_URL;
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("vendor Refresh Token API Tests", () => {
      // Test case for refresh token
      this.framework.addTestCase("vendor Refresh Token API", async () => {
        const response = await this.framework.makeRequest(
          `${this.baseUrl}/v1/api/thinkpartnership/vendor/lambda/refresh_token`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              user_id: `${Math.floor(Math.random() * 100000)}`,
              refresh_token: "abcthinkpartnership",
            }),
          }
        );

        // Assertions for the health check
        this.framework.assert(
          response.status === 200 || response.status === 401,
          "Refresh Token vendor should return 200 status or 401 status for invalid token"
        );

        this.framework.assert(
          response.body.error === false ||
            response.body.message === "Token Invalid",
          'Refresh Token vendor error flag should be false or message should be "Token Invalid"'
        );
      });

      // Add more test cases here as needed
      this.framework.addTestCase(
        "vendor Refresh Token API - Invalid Token",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/v1/api/thinkpartnership/vendor/lambda/refresh_token`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                user_id: `${Math.floor(Math.random() * 100000)}`,
                refresh_token: "invalid-token",
              }),
            }
          );

          // Assertions for invalid token
          this.framework.assert(
            response.status === 401,
            "Refresh Token with invalid token should return 401 status"
          );

          this.framework.assert(
            response.body.error === true,
            "Refresh Token with invalid token should have error flag set to true"
          );

          this.framework.assert(
            response.body.message === "Token Invalid",
            'Refresh Token with invalid token should return "Token Invalid" message'
          );
        }
      );
    });
  }

  // Helper method to run all tests
  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new RefreshTokenTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
