const APITestFramework = require("../../../../tests/apitesting.base.js");

const BASE_URL = "http://localhost:5172";

/**
 * Register API Tests
 * Class-based implementation of the Register API tests
 */
class RegisterTests {
  constructor() {
    this.framework = new APITestFramework();
    this.baseUrl = BASE_URL;
    this.setupTests();
  }

  setupTests() {
    this.framework.describe("vendor Register API Tests", () => {
      // Test case for successful registration
      this.framework.addTestCase("vendor Register API - Success", async () => {
        const response = await this.framework.makeRequest(
          `${this.baseUrl}/v1/api/thinkpartnership/vendor/lambda/register`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              email: `testuser${Math.floor(
                Math.random() * 100000
              )}@manaknight.com`,
              password: "a123456",
              first_name: "vendor",
              last_name: "User",
              phone: "",
            }),
          }
        );

        // Assertions for the health check
        this.framework.assert(
          response.status === 200,
          "Register vendor should return 200 status"
        );
        this.framework.assert(
          response.body.token !== undefined,
          "Register vendor response should have token"
        );
        this.framework.assert(
          response.body.error === false,
          "Register vendor error flag should be false"
        );
      });

      // Add more test cases here as needed
      this.framework.addTestCase(
        "Register vendor API - Validation Error",
        async () => {
          const response = await this.framework.makeRequest(
            `${this.baseUrl}/v1/api/thinkpartnership/vendor/lambda/register`,
            {
              method: "POST",
              headers: {
                "Content-Type": "application/json",
              },
              body: JSON.stringify({
                email: "invalid-email",
                password: "short",
                first_name: "",
                last_name: "",
                phone: "",
              }),
            }
          );

          // Assertions for validation error
          this.framework.assert(
            response.status === 400,
            "Register with invalid data should return 400 status"
          );
          this.framework.assert(
            response.body.error === true,
            "Register with invalid data should have error flag set to true"
          );
          this.framework.assert(
            response.body.validation !== undefined,
            "Register with invalid data should return validation errors"
          );
        }
      );
    });
  }

  // Helper method to run all tests
  async runTests() {
    try {
      // Run the tests and return the results directly
      return await this.framework.runTests();
    } catch (error) {
      console.error("Test execution failed:", error);
      throw error;
    }
  }
}

// Create an instance of the test class and run the tests
const tests = new RegisterTests();
module.exports = tests
  .runTests()
  .then((report) => {
    if (report.failed > 0) {
      process.exit(1);
    }
    return report; // Important: return the report for the test runner
  })
  .catch((error) => {
    console.error("Test framework error:", error);
    process.exit(1);
  });
