{"info": {"name": "thinkpartnership API Collection", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "description": "API collection for thinkpartnership project", "_postman_id": "1750871921550"}, "item": [{"name": "Lambda Endpoints", "description": "API endpoints defined in Lambda functions", "item": [{"name": "2FA Login API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"role\": \"user\", \"qr_code\": \"base64_qr_code\", \"one_time_token\": \"token\", \"expire_at\": 60, \"user_id\": 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"wrongpassword\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Failed to login\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"email\", \"message\": \"Email required\"},{\"field\": \"password\", \"message\": \"Password required\"},{\"field\": \"role\", \"message\": \"Role required\"}]}"}]}, {"name": "2FA Verify API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/admin/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "admin", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_code\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/admin/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "admin", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_code\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"valid\": true, \"message\": \"Verified Successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/admin/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "admin", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"invalid_code\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"valid\":false,\"message\":\"Invalid Token\",\"validation\":[{\"field\": \"token\", \"message\": \"Invalid Token\"}]}"}]}, {"name": "2FA Authorization API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/admin/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "admin", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"verification_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/admin/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "admin", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"verification_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"role\": \"user\", \"token\": \"access_token\", \"expire_at\": 3600, \"user_id\": 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/admin/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "admin", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"invalid_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credential\",\"validation\":[{\"field\": \"code\", \"message\": \"Invalid Credentials\"}]}"}]}, {"name": "Apple Code API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": {"key": "state", "value": "projectId~secret"}, "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Apple Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to apple login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Google Code API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Google Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to google login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "microsoft Code API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "microsoft Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to microsoft login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Get Preference", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"user_id\": 1, \"name\": \"preference\", \"value\": \"value\"}}"}]}, {"name": "Update Preference", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Updated\"}"}]}, {"name": "User Profile API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"Test User\", \"created_at\": \"2023-01-01T00:00:00.000Z\"}}"}, {"name": "Error Response - 401", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Authentication required\"}"}]}, {"name": "Update User Profile API", "request": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Profile updated successfully\", \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\"}}"}, {"name": "Error Response - 401", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Authentication required\"}"}, {"name": "Error Response - 400", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"No profile data provided\"}"}]}, {"name": "Refresh Token API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,\"access_token\":\"New JWT Token\",\"refresh_token\":\"New JWT Token\"}"}, {"name": "Error Response - 401", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Token Invalid\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Refresh token missing\",validation: [{ field: \"refresh_token\", message: \"Refresh token missing\" }] }"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "User Registration API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"New User\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"New User\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Registration successful\", \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"New User\"}}"}, {"name": "Error Response - 409", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"Existing User\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 409, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Email already exists\"}"}, {"name": "Error Response - 400", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid registration data\",\"validation\":[{\"field\": \"email\", \"message\": \"Email is required\"}, {\"field\": \"password\", \"message\": \"Password is required\"}]}"}]}, {"name": "GET single or many record", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/admin/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "admin", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/admin/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "admin", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"mappings\": {\"table1\": {\"field1\": \"field1\", \"field2\": \"field2\"}, \"table2\": {\"field3\": \"field3\", \"field4\": \"field4\"}}, \"list\": [{\"id\": 1, \"name\": \"<PERSON>\"}, {\"id\": 2, \"name\": \"<PERSON>\"}]}"}]}, {"name": "LIST/PAGINATE records", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/admin/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "admin", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/admin/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "admin", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"list\": [{\"id\": 1, \"name\": \"<PERSON>\"}, {\"id\": 2, \"name\": \"<PERSON>\"}]}"}]}, {"name": "CREATE record", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/admin/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "admin", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/admin/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "admin", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"name\": \"<PERSON>\"}}"}]}, {"name": "UPDATE record", "request": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/admin/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "admin", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/admin/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "admin", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"name\": \"<PERSON>\"}}"}]}, {"name": "DELETE record", "request": {"method": "DELETE", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/admin/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "admin", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "DELETE", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/admin/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "admin", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Record deleted successfully\"}"}]}, {"name": "Email Update API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,message: \"updated\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"email\", \"message\": \"Email missing\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{'email': '<EMAIL>'}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Email Exist Already\",\"validation\":[{\"field\": \"email\", \"message\": \"Email Exist Already\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{'email': '<EMAIL>'}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "Password Update API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,message: \"Password updated successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"password\", \"message\": \"Password missing\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "Email Verification API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_token\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Email verified successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"invalid_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid verification token\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/admin/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "admin", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Token is required\",\"validation\":[{\"field\": \"token\", \"message\": \"Token is required\"}]}"}]}, {"name": "2FA Login API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"role\": \"user\", \"qr_code\": \"base64_qr_code\", \"one_time_token\": \"token\", \"expire_at\": 60, \"user_id\": 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"wrongpassword\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Failed to login\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"email\", \"message\": \"Email required\"},{\"field\": \"password\", \"message\": \"Password required\"},{\"field\": \"role\", \"message\": \"Role required\"}]}"}]}, {"name": "2FA Verify API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/client/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "client", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_code\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/client/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "client", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_code\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"valid\": true, \"message\": \"Verified Successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/client/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "client", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"invalid_code\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"valid\":false,\"message\":\"Invalid Token\",\"validation\":[{\"field\": \"token\", \"message\": \"Invalid Token\"}]}"}]}, {"name": "2FA Authorization API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/client/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "client", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"verification_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/client/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "client", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"verification_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"role\": \"user\", \"token\": \"access_token\", \"expire_at\": 3600, \"user_id\": 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/client/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "client", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"invalid_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credential\",\"validation\":[{\"field\": \"code\", \"message\": \"Invalid Credentials\"}]}"}]}, {"name": "Apple Code API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": {"key": "state", "value": "projectId~secret"}, "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Apple Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to apple login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Google Code API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Google Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to google login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "microsoft Code API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "microsoft Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to microsoft login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Get Preference", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"user_id\": 1, \"name\": \"preference\", \"value\": \"value\"}}"}]}, {"name": "Update Preference", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Updated\"}"}]}, {"name": "User Profile API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"Test User\", \"created_at\": \"2023-01-01T00:00:00.000Z\"}}"}, {"name": "Error Response - 401", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Authentication required\"}"}]}, {"name": "Update User Profile API", "request": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Profile updated successfully\", \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\"}}"}, {"name": "Error Response - 401", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Authentication required\"}"}, {"name": "Error Response - 400", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"No profile data provided\"}"}]}, {"name": "Refresh Token API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,\"access_token\":\"New JWT Token\",\"refresh_token\":\"New JWT Token\"}"}, {"name": "Error Response - 401", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Token Invalid\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Refresh token missing\",validation: [{ field: \"refresh_token\", message: \"Refresh token missing\" }] }"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "User Registration API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"New User\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"New User\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Registration successful\", \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"New User\"}}"}, {"name": "Error Response - 409", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"Existing User\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 409, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Email already exists\"}"}, {"name": "Error Response - 400", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid registration data\",\"validation\":[{\"field\": \"email\", \"message\": \"Email is required\"}, {\"field\": \"password\", \"message\": \"Password is required\"}]}"}]}, {"name": "GET single or many record", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/client/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "client", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/client/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "client", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"mappings\": {\"table1\": {\"field1\": \"field1\", \"field2\": \"field2\"}, \"table2\": {\"field3\": \"field3\", \"field4\": \"field4\"}}, \"list\": [{\"id\": 1, \"name\": \"<PERSON>\"}, {\"id\": 2, \"name\": \"<PERSON>\"}]}"}]}, {"name": "LIST/PAGINATE records", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/client/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "client", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/client/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "client", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"list\": [{\"id\": 1, \"name\": \"<PERSON>\"}, {\"id\": 2, \"name\": \"<PERSON>\"}]}"}]}, {"name": "CREATE record", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/client/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "client", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/client/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "client", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"name\": \"<PERSON>\"}}"}]}, {"name": "UPDATE record", "request": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/client/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "client", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/client/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "client", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"name\": \"<PERSON>\"}}"}]}, {"name": "DELETE record", "request": {"method": "DELETE", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/client/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "client", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "DELETE", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/client/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "client", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Record deleted successfully\"}"}]}, {"name": "Email Update API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,message: \"updated\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"email\", \"message\": \"Email missing\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{'email': '<EMAIL>'}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Email Exist Already\",\"validation\":[{\"field\": \"email\", \"message\": \"Email Exist Already\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{'email': '<EMAIL>'}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "Password Update API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,message: \"Password updated successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"password\", \"message\": \"Password missing\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "Email Verification API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_token\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Email verified successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"invalid_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid verification token\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/client/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "client", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Token is required\",\"validation\":[{\"field\": \"token\", \"message\": \"Token is required\"}]}"}]}, {"name": "2FA Login API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"role\": \"user\", \"qr_code\": \"base64_qr_code\", \"one_time_token\": \"token\", \"expire_at\": 60, \"user_id\": 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"wrongpassword\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Failed to login\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"email\", \"message\": \"Email required\"},{\"field\": \"password\", \"message\": \"Password required\"},{\"field\": \"role\", \"message\": \"Role required\"}]}"}]}, {"name": "2FA Verify API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/user/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "user", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_code\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/user/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "user", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_code\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"valid\": true, \"message\": \"Verified Successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/user/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "user", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"invalid_code\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"valid\":false,\"message\":\"Invalid Token\",\"validation\":[{\"field\": \"token\", \"message\": \"Invalid Token\"}]}"}]}, {"name": "2FA Authorization API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/user/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "user", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"verification_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/user/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "user", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"verification_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"role\": \"user\", \"token\": \"access_token\", \"expire_at\": 3600, \"user_id\": 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/user/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "user", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"invalid_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credential\",\"validation\":[{\"field\": \"code\", \"message\": \"Invalid Credentials\"}]}"}]}, {"name": "Apple Code API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": {"key": "state", "value": "projectId~secret"}, "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Apple Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to apple login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Google Code API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Google Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to google login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "microsoft Code API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "microsoft Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to microsoft login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Get Preference", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"user_id\": 1, \"name\": \"preference\", \"value\": \"value\"}}"}]}, {"name": "Update Preference", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Updated\"}"}]}, {"name": "User Profile API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"Test User\", \"created_at\": \"2023-01-01T00:00:00.000Z\"}}"}, {"name": "Error Response - 401", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Authentication required\"}"}]}, {"name": "Update User Profile API", "request": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Profile updated successfully\", \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\"}}"}, {"name": "Error Response - 401", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Authentication required\"}"}, {"name": "Error Response - 400", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"No profile data provided\"}"}]}, {"name": "Refresh Token API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,\"access_token\":\"New JWT Token\",\"refresh_token\":\"New JWT Token\"}"}, {"name": "Error Response - 401", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Token Invalid\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Refresh token missing\",validation: [{ field: \"refresh_token\", message: \"Refresh token missing\" }] }"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "User Registration API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"New User\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"New User\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Registration successful\", \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"New User\"}}"}, {"name": "Error Response - 409", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"Existing User\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 409, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Email already exists\"}"}, {"name": "Error Response - 400", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid registration data\",\"validation\":[{\"field\": \"email\", \"message\": \"Email is required\"}, {\"field\": \"password\", \"message\": \"Password is required\"}]}"}]}, {"name": "GET single or many record", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/user/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "user", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/user/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "user", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"mappings\": {\"table1\": {\"field1\": \"field1\", \"field2\": \"field2\"}, \"table2\": {\"field3\": \"field3\", \"field4\": \"field4\"}}, \"list\": [{\"id\": 1, \"name\": \"<PERSON>\"}, {\"id\": 2, \"name\": \"<PERSON>\"}]}"}]}, {"name": "LIST/PAGINATE records", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/user/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "user", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/user/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "user", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"list\": [{\"id\": 1, \"name\": \"<PERSON>\"}, {\"id\": 2, \"name\": \"<PERSON>\"}]}"}]}, {"name": "CREATE record", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/user/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "user", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/user/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "user", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"name\": \"<PERSON>\"}}"}]}, {"name": "UPDATE record", "request": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/user/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "user", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/user/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "user", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"name\": \"<PERSON>\"}}"}]}, {"name": "DELETE record", "request": {"method": "DELETE", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/user/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "user", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "DELETE", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/user/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "user", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Record deleted successfully\"}"}]}, {"name": "Email Update API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,message: \"updated\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"email\", \"message\": \"Email missing\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{'email': '<EMAIL>'}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Email Exist Already\",\"validation\":[{\"field\": \"email\", \"message\": \"Email Exist Already\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{'email': '<EMAIL>'}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "Password Update API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,message: \"Password updated successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"password\", \"message\": \"Password missing\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "Email Verification API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_token\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Email verified successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"invalid_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid verification token\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/user/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "user", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Token is required\",\"validation\":[{\"field\": \"token\", \"message\": \"Token is required\"}]}"}]}, {"name": "2FA Login API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"role\": \"user\", \"qr_code\": \"base64_qr_code\", \"one_time_token\": \"token\", \"expire_at\": 60, \"user_id\": 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"wrongpassword\", \"role\": \"user\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Failed to login\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/2fa/login", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "2fa", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"email\", \"message\": \"Email required\"},{\"field\": \"password\", \"message\": \"Password required\"},{\"field\": \"role\", \"message\": \"Role required\"}]}"}]}, {"name": "2FA Verify API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/vendor/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "vendor", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_code\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/vendor/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "vendor", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_code\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"valid\": true, \"message\": \"Verified Successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/vendor/lambda/2fa/verify", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "vendor", "lambda", "2fa", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"invalid_code\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"valid\":false,\"message\":\"Invalid Token\",\"validation\":[{\"field\": \"token\", \"message\": \"Invalid Token\"}]}"}]}, {"name": "2FA Authorization API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/vendor/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "vendor", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"verification_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/vendor/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "vendor", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"verification_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"role\": \"user\", \"token\": \"access_token\", \"expire_at\": 3600, \"user_id\": 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v2/api/thinkpartnership/vendor/lambda/2fa/auth", "host": ["{{baseUrl}}"], "path": ["v2", "api", "thinkpartnership", "vendor", "lambda", "2fa", "auth"]}, "body": {"mode": "raw", "raw": "{ \"code\": \"invalid_code\", \"token\": \"one_time_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credential\",\"validation\":[{\"field\": \"code\", \"message\": \"Invalid Credentials\"}]}"}]}, {"name": "Apple Code API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": {"key": "state", "value": "projectId~secret"}, "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Apple Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to apple login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/apple/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "apple", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Google Code API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Google Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to google login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/google/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "google", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "microsoft Code API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{error: false, role: 'admin', token: 'jwt token', expire_at: 60, user_id: 1}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/code", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "code"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "microsoft Login API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "['Will redirect to microsoft login with auth link']"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"access token\", \"message\": \"Something went wrong\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v2/api/lambda/microsoft/login", "host": ["{{baseUrl}}"], "path": ["v2", "api", "lambda", "microsoft", "login"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true, \"failure\": \"me\", \"message\": \"Something went wrong\"}"}]}, {"name": "Get Preference", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"user_id\": 1, \"name\": \"preference\", \"value\": \"value\"}}"}]}, {"name": "Update Preference", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/preference", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "preference"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Updated\"}"}]}, {"name": "User Profile API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"Test User\", \"created_at\": \"2023-01-01T00:00:00.000Z\"}}"}, {"name": "Error Response - 401", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Authentication required\"}"}]}, {"name": "Update User Profile API", "request": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Profile updated successfully\", \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"Updated Name\", \"profile_image\": \"https://example.com/image.jpg\"}}"}, {"name": "Error Response - 401", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{ \"name\": \"Updated Name\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Authentication required\"}"}, {"name": "Error Response - 400", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/profile", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "profile"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"No profile data provided\"}"}]}, {"name": "Refresh Token API", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,\"access_token\":\"New JWT Token\",\"refresh_token\":\"New JWT Token\"}"}, {"name": "Error Response - 401", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 401, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Token Invalid\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Refresh token missing\",validation: [{ field: \"refresh_token\", message: \"Refresh token missing\" }] }"}, {"name": "Error Response - 403", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/refresh_token", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "refresh_token"]}, "body": {"mode": "raw", "raw": "{'refresh_token': 'JWT refresh token', 'user_id': 1}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "User Registration API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"New User\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"New User\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Registration successful\", \"user\": {\"id\": 1, \"email\": \"<EMAIL>\", \"name\": \"New User\"}}"}, {"name": "Error Response - 409", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\", \"password\": \"password123\", \"name\": \"Existing User\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 409, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Email already exists\"}"}, {"name": "Error Response - 400", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/register", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "register"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 400, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid registration data\",\"validation\":[{\"field\": \"email\", \"message\": \"Email is required\"}, {\"field\": \"password\", \"message\": \"Password is required\"}]}"}]}, {"name": "GET single or many record", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/vendor/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "vendor", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/vendor/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "vendor", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"mappings\": {\"table1\": {\"field1\": \"field1\", \"field2\": \"field2\"}, \"table2\": {\"field3\": \"field3\", \"field4\": \"field4\"}}, \"list\": [{\"id\": 1, \"name\": \"<PERSON>\"}, {\"id\": 2, \"name\": \"<PERSON>\"}]}"}]}, {"name": "LIST/PAGINATE records", "request": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/vendor/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "vendor", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "GET", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/vendor/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "vendor", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"list\": [{\"id\": 1, \"name\": \"<PERSON>\"}, {\"id\": 2, \"name\": \"<PERSON>\"}]}"}]}, {"name": "CREATE record", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/vendor/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "vendor", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/vendor/:table", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "vendor", ":table"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"name\": \"<PERSON>\"}}"}]}, {"name": "UPDATE record", "request": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/vendor/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "vendor", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "PUT", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/vendor/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "vendor", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"model\": {\"id\": 1, \"name\": \"<PERSON>\"}}"}]}, {"name": "DELETE record", "request": {"method": "DELETE", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/vendor/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "vendor", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "DELETE", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/records/thinkpartnership/vendor/:table/:id", "host": ["{{baseUrl}}"], "path": ["v1", "api", "records", "thinkpartnership", "vendor", ":table", ":id"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Record deleted successfully\"}"}]}, {"name": "Email Update API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,message: \"updated\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{ \"email\": \"<EMAIL>\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"email\", \"message\": \"Email missing\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{'email': '<EMAIL>'}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Email Exist Already\",\"validation\":[{\"field\": \"email\", \"message\": \"Email Exist Already\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/email", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "email"]}, "body": {"mode": "raw", "raw": "{'email': '<EMAIL>'}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "Password Update API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false,message: \"Password updated successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid Credentials\",\"validation\":[{\"field\": \"password\", \"message\": \"Password missing\"}]}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [{"key": "Authorization", "value": "Bearer {{authToken}}", "type": "text"}], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/update/password", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "update", "password"]}, "body": {"mode": "raw", "raw": "{ \"password\": \"a12345\"}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\": true,\"message\": \"Some error message\"}"}]}, {"name": "Email Verification API", "request": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_token\" }", "options": {"raw": {"language": "json"}}}}, "response": [{"name": "Success Response", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"verification_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "OK", "code": 200, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":false, \"message\": \"Email verified successfully\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{ \"token\": \"invalid_token\" }", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Invalid verification token\"}"}, {"name": "Error Response - 403", "originalRequest": {"method": "POST", "description": "Lambda API from undefined", "header": [], "url": {"raw": "{{baseUrl}}/v1/api/thinkpartnership/vendor/lambda/verify", "host": ["{{baseUrl}}"], "path": ["v1", "api", "thinkpartnership", "vendor", "lambda", "verify"]}, "body": {"mode": "raw", "raw": "{}", "options": {"raw": {"language": "json"}}}}, "status": "Error", "code": 403, "_postman_previewlanguage": "json", "header": [{"key": "Content-Type", "value": "application/json"}], "body": "{\"error\":true,\"message\":\"Token is required\",\"validation\":[{\"field\": \"token\", \"message\": \"Token is required\"}]}"}]}]}], "event": [], "variable": [{"key": "authToken", "value": "", "type": "string", "description": "Authentication token for requests"}, {"key": "baseUrl", "value": "https://baas.mytechpassport.com", "type": "string", "description": "Base URL for API requests"}]}