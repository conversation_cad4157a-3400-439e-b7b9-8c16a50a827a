{"version": "2.0", "metadata": {"apiVersion": "2016-04-18", "endpointPrefix": "cognito-idp", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceFullName": "Amazon Cognito Identity Provider", "serviceId": "Cognito Identity Provider", "signatureVersion": "v4", "targetPrefix": "AWSCognitoIdentityProviderService", "uid": "cognito-idp-2016-04-18", "auth": ["aws.auth#sigv4"]}, "operations": {"AddCustomAttributes": {"input": {"type": "structure", "required": ["UserPoolId", "CustomAttributes"], "members": {"UserPoolId": {}, "CustomAttributes": {"type": "list", "member": {"shape": "S4"}}}}, "output": {"type": "structure", "members": {}}}, "AdminAddUserToGroup": {"input": {"type": "structure", "required": ["UserPoolId", "Username", "GroupName"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "GroupName": {}}}}, "AdminConfirmSignUp": {"input": {"type": "structure", "required": ["UserPoolId", "Username"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {}}}, "AdminCreateUser": {"input": {"type": "structure", "required": ["UserPoolId", "Username"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "UserAttributes": {"shape": "Sj"}, "ValidationData": {"shape": "Sj"}, "TemporaryPassword": {"shape": "Sn"}, "ForceAliasCreation": {"type": "boolean"}, "MessageAction": {}, "DesiredDeliveryMediums": {"type": "list", "member": {}}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {"User": {"shape": "St"}}}}, "AdminDeleteUser": {"input": {"type": "structure", "required": ["UserPoolId", "Username"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}}}}, "AdminDeleteUserAttributes": {"input": {"type": "structure", "required": ["UserPoolId", "Username", "UserAttributeNames"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "UserAttributeNames": {"shape": "S10"}}}, "output": {"type": "structure", "members": {}}}, "AdminDisableProviderForUser": {"input": {"type": "structure", "required": ["UserPoolId", "User"], "members": {"UserPoolId": {}, "User": {"shape": "S13"}}}, "output": {"type": "structure", "members": {}}}, "AdminDisableUser": {"input": {"type": "structure", "required": ["UserPoolId", "Username"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}}}, "output": {"type": "structure", "members": {}}}, "AdminEnableUser": {"input": {"type": "structure", "required": ["UserPoolId", "Username"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}}}, "output": {"type": "structure", "members": {}}}, "AdminForgetDevice": {"input": {"type": "structure", "required": ["UserPoolId", "Username", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "DeviceKey": {}}}}, "AdminGetDevice": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>", "UserPoolId", "Username"], "members": {"DeviceKey": {}, "UserPoolId": {}, "Username": {"shape": "Sd"}}}, "output": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Device": {"shape": "S1e"}}}}, "AdminGetUser": {"input": {"type": "structure", "required": ["UserPoolId", "Username"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}}}, "output": {"type": "structure", "required": ["Username"], "members": {"Username": {"shape": "Sd"}, "UserAttributes": {"shape": "Sj"}, "UserCreateDate": {"type": "timestamp"}, "UserLastModifiedDate": {"type": "timestamp"}, "Enabled": {"type": "boolean"}, "UserStatus": {}, "MFAOptions": {"shape": "Sw"}, "PreferredMfaSetting": {}, "UserMFASettingList": {"shape": "S1h"}}}}, "AdminInitiateAuth": {"input": {"type": "structure", "required": ["UserPoolId", "ClientId", "AuthFlow"], "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}, "AuthFlow": {}, "AuthParameters": {"shape": "S1l"}, "ClientMetadata": {"shape": "Sg"}, "AnalyticsMetadata": {"shape": "S1m"}, "ContextData": {"shape": "S1n"}}}, "output": {"type": "structure", "members": {"ChallengeName": {}, "Session": {"shape": "S1s"}, "ChallengeParameters": {"shape": "S1t"}, "AuthenticationResult": {"shape": "S1u"}}}}, "AdminLinkProviderForUser": {"input": {"type": "structure", "required": ["UserPoolId", "DestinationUser", "SourceUser"], "members": {"UserPoolId": {}, "DestinationUser": {"shape": "S13"}, "SourceUser": {"shape": "S13"}}}, "output": {"type": "structure", "members": {}}}, "AdminListDevices": {"input": {"type": "structure", "required": ["UserPoolId", "Username"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "Limit": {"type": "integer"}, "PaginationToken": {}}}, "output": {"type": "structure", "members": {"Devices": {"shape": "S24"}, "PaginationToken": {}}}}, "AdminListGroupsForUser": {"input": {"type": "structure", "required": ["Username", "UserPoolId"], "members": {"Username": {"shape": "Sd"}, "UserPoolId": {}, "Limit": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Groups": {"shape": "S28"}, "NextToken": {}}}}, "AdminListUserAuthEvents": {"input": {"type": "structure", "required": ["UserPoolId", "Username"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"AuthEvents": {"type": "list", "member": {"type": "structure", "members": {"EventId": {}, "EventType": {}, "CreationDate": {"type": "timestamp"}, "EventResponse": {}, "EventRisk": {"type": "structure", "members": {"RiskDecision": {}, "RiskLevel": {}, "CompromisedCredentialsDetected": {"type": "boolean"}}}, "ChallengeResponses": {"type": "list", "member": {"type": "structure", "members": {"ChallengeName": {}, "ChallengeResponse": {}}}}, "EventContextData": {"type": "structure", "members": {"IpAddress": {}, "DeviceName": {}, "Timezone": {}, "City": {}, "Country": {}}}, "EventFeedback": {"type": "structure", "required": ["Feedback<PERSON><PERSON><PERSON>", "Provider"], "members": {"FeedbackValue": {}, "Provider": {}, "FeedbackDate": {"type": "timestamp"}}}}}}, "NextToken": {}}}}, "AdminRemoveUserFromGroup": {"input": {"type": "structure", "required": ["UserPoolId", "Username", "GroupName"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "GroupName": {}}}}, "AdminResetUserPassword": {"input": {"type": "structure", "required": ["UserPoolId", "Username"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {}}}, "AdminRespondToAuthChallenge": {"input": {"type": "structure", "required": ["UserPoolId", "ClientId", "ChallengeName"], "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}, "ChallengeName": {}, "ChallengeResponses": {"shape": "S2y"}, "Session": {"shape": "S1s"}, "AnalyticsMetadata": {"shape": "S1m"}, "ContextData": {"shape": "S1n"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {"ChallengeName": {}, "Session": {"shape": "S1s"}, "ChallengeParameters": {"shape": "S1t"}, "AuthenticationResult": {"shape": "S1u"}}}}, "AdminSetUserMFAPreference": {"input": {"type": "structure", "required": ["Username", "UserPoolId"], "members": {"SMSMfaSettings": {"shape": "S31"}, "SoftwareTokenMfaSettings": {"shape": "S32"}, "Username": {"shape": "Sd"}, "UserPoolId": {}}}, "output": {"type": "structure", "members": {}}}, "AdminSetUserPassword": {"input": {"type": "structure", "required": ["UserPoolId", "Username", "Password"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "Password": {"shape": "Sn"}, "Permanent": {"type": "boolean"}}}, "output": {"type": "structure", "members": {}}}, "AdminSetUserSettings": {"input": {"type": "structure", "required": ["UserPoolId", "Username", "MFAOptions"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "MFAOptions": {"shape": "Sw"}}}, "output": {"type": "structure", "members": {}}}, "AdminUpdateAuthEventFeedback": {"input": {"type": "structure", "required": ["UserPoolId", "Username", "EventId", "Feedback<PERSON><PERSON><PERSON>"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "EventId": {}, "FeedbackValue": {}}}, "output": {"type": "structure", "members": {}}}, "AdminUpdateDeviceStatus": {"input": {"type": "structure", "required": ["UserPoolId", "Username", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "DeviceKey": {}, "DeviceRememberedStatus": {}}}, "output": {"type": "structure", "members": {}}}, "AdminUpdateUserAttributes": {"input": {"type": "structure", "required": ["UserPoolId", "Username", "UserAttributes"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "UserAttributes": {"shape": "Sj"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {}}}, "AdminUserGlobalSignOut": {"input": {"type": "structure", "required": ["UserPoolId", "Username"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}}}, "output": {"type": "structure", "members": {}}}, "AssociateSoftwareToken": {"input": {"type": "structure", "members": {"AccessToken": {"shape": "S1v"}, "Session": {"shape": "S1s"}}}, "output": {"type": "structure", "members": {"SecretCode": {"type": "string", "sensitive": true}, "Session": {"shape": "S1s"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "ChangePassword": {"input": {"type": "structure", "required": ["PreviousPassword", "ProposedPassword", "AccessToken"], "members": {"PreviousPassword": {"shape": "Sn"}, "ProposedPassword": {"shape": "Sn"}, "AccessToken": {"shape": "S1v"}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "ConfirmDevice": {"input": {"type": "structure", "required": ["AccessToken", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"AccessToken": {"shape": "S1v"}, "DeviceKey": {}, "DeviceSecretVerifierConfig": {"type": "structure", "members": {"PasswordVerifier": {}, "Salt": {}}}, "DeviceName": {}}}, "output": {"type": "structure", "members": {"UserConfirmationNecessary": {"type": "boolean"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "ConfirmForgotPassword": {"input": {"type": "structure", "required": ["ClientId", "Username", "ConfirmationCode", "Password"], "members": {"ClientId": {"shape": "S1j"}, "SecretHash": {"shape": "S3s"}, "Username": {"shape": "Sd"}, "ConfirmationCode": {}, "Password": {"shape": "Sn"}, "AnalyticsMetadata": {"shape": "S1m"}, "UserContextData": {"shape": "S3u"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "ConfirmSignUp": {"input": {"type": "structure", "required": ["ClientId", "Username", "ConfirmationCode"], "members": {"ClientId": {"shape": "S1j"}, "SecretHash": {"shape": "S3s"}, "Username": {"shape": "Sd"}, "ConfirmationCode": {}, "ForceAliasCreation": {"type": "boolean"}, "AnalyticsMetadata": {"shape": "S1m"}, "UserContextData": {"shape": "S3u"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "CreateGroup": {"input": {"type": "structure", "required": ["GroupName", "UserPoolId"], "members": {"GroupName": {}, "UserPoolId": {}, "Description": {}, "RoleArn": {}, "Precedence": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Group": {"shape": "S29"}}}}, "CreateIdentityProvider": {"input": {"type": "structure", "required": ["UserPoolId", "ProviderName", "ProviderType", "ProviderDetails"], "members": {"UserPoolId": {}, "ProviderName": {}, "ProviderType": {}, "ProviderDetails": {"shape": "S43"}, "AttributeMapping": {"shape": "S44"}, "IdpIdentifiers": {"shape": "S46"}}}, "output": {"type": "structure", "required": ["IdentityProvider"], "members": {"IdentityProvider": {"shape": "S49"}}}}, "CreateResourceServer": {"input": {"type": "structure", "required": ["UserPoolId", "Identifier", "Name"], "members": {"UserPoolId": {}, "Identifier": {}, "Name": {}, "Scopes": {"shape": "S4d"}}}, "output": {"type": "structure", "required": ["ResourceServer"], "members": {"ResourceServer": {"shape": "S4i"}}}}, "CreateUserImportJob": {"input": {"type": "structure", "required": ["JobName", "UserPoolId", "CloudWatchLogsRoleArn"], "members": {"JobName": {}, "UserPoolId": {}, "CloudWatchLogsRoleArn": {}}}, "output": {"type": "structure", "members": {"UserImportJob": {"shape": "S4m"}}}}, "CreateUserPool": {"input": {"type": "structure", "required": ["PoolName"], "members": {"PoolName": {}, "Policies": {"shape": "S4u"}, "DeletionProtection": {}, "LambdaConfig": {"shape": "S50"}, "AutoVerifiedAttributes": {"shape": "S57"}, "AliasAttributes": {"shape": "S59"}, "UsernameAttributes": {"shape": "S5b"}, "SmsVerificationMessage": {}, "EmailVerificationMessage": {}, "EmailVerificationSubject": {}, "VerificationMessageTemplate": {"shape": "S5g"}, "SmsAuthenticationMessage": {}, "MfaConfiguration": {}, "UserAttributeUpdateSettings": {"shape": "S5l"}, "DeviceConfiguration": {"shape": "S5n"}, "EmailConfiguration": {"shape": "S5o"}, "SmsConfiguration": {"shape": "S5s"}, "UserPoolTags": {"shape": "S5u"}, "AdminCreateUserConfig": {"shape": "S5x"}, "Schema": {"shape": "S60"}, "UserPoolAddOns": {"shape": "S61"}, "UsernameConfiguration": {"shape": "S65"}, "AccountRecoverySetting": {"shape": "S66"}}}, "output": {"type": "structure", "members": {"UserPool": {"shape": "S6c"}}}}, "CreateUserPoolClient": {"input": {"type": "structure", "required": ["UserPoolId", "ClientName"], "members": {"UserPoolId": {}, "ClientName": {}, "GenerateSecret": {"type": "boolean"}, "RefreshTokenValidity": {"type": "integer"}, "AccessTokenValidity": {"type": "integer"}, "IdTokenValidity": {"type": "integer"}, "TokenValidityUnits": {"shape": "S6l"}, "ReadAttributes": {"shape": "S6n"}, "WriteAttributes": {"shape": "S6n"}, "ExplicitAuthFlows": {"shape": "S6p"}, "SupportedIdentityProviders": {"shape": "S6r"}, "CallbackURLs": {"shape": "S6s"}, "LogoutURLs": {"shape": "S6u"}, "DefaultRedirectURI": {}, "AllowedOAuthFlows": {"shape": "S6v"}, "AllowedOAuthScopes": {"shape": "S6x"}, "AllowedOAuthFlowsUserPoolClient": {"type": "boolean"}, "AnalyticsConfiguration": {"shape": "S6z"}, "PreventUserExistenceErrors": {}, "EnableTokenRevocation": {"type": "boolean"}, "EnablePropagateAdditionalUserContextData": {"type": "boolean"}, "AuthSessionValidity": {"type": "integer"}}}, "output": {"type": "structure", "members": {"UserPoolClient": {"shape": "S74"}}}}, "CreateUserPoolDomain": {"input": {"type": "structure", "required": ["Domain", "UserPoolId"], "members": {"Domain": {}, "UserPoolId": {}, "CustomDomainConfig": {"shape": "S77"}}}, "output": {"type": "structure", "members": {"CloudFrontDomain": {}}}}, "DeleteGroup": {"input": {"type": "structure", "required": ["GroupName", "UserPoolId"], "members": {"GroupName": {}, "UserPoolId": {}}}}, "DeleteIdentityProvider": {"input": {"type": "structure", "required": ["UserPoolId", "ProviderName"], "members": {"UserPoolId": {}, "ProviderName": {}}}}, "DeleteResourceServer": {"input": {"type": "structure", "required": ["UserPoolId", "Identifier"], "members": {"UserPoolId": {}, "Identifier": {}}}}, "DeleteUser": {"input": {"type": "structure", "required": ["AccessToken"], "members": {"AccessToken": {"shape": "S1v"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "DeleteUserAttributes": {"input": {"type": "structure", "required": ["UserAttributeNames", "AccessToken"], "members": {"UserAttributeNames": {"shape": "S10"}, "AccessToken": {"shape": "S1v"}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "DeleteUserPool": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}}}}, "DeleteUserPoolClient": {"input": {"type": "structure", "required": ["UserPoolId", "ClientId"], "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}}}}, "DeleteUserPoolDomain": {"input": {"type": "structure", "required": ["Domain", "UserPoolId"], "members": {"Domain": {}, "UserPoolId": {}}}, "output": {"type": "structure", "members": {}}}, "DescribeIdentityProvider": {"input": {"type": "structure", "required": ["UserPoolId", "ProviderName"], "members": {"UserPoolId": {}, "ProviderName": {}}}, "output": {"type": "structure", "required": ["IdentityProvider"], "members": {"IdentityProvider": {"shape": "S49"}}}}, "DescribeResourceServer": {"input": {"type": "structure", "required": ["UserPoolId", "Identifier"], "members": {"UserPoolId": {}, "Identifier": {}}}, "output": {"type": "structure", "required": ["ResourceServer"], "members": {"ResourceServer": {"shape": "S4i"}}}}, "DescribeRiskConfiguration": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}}}, "output": {"type": "structure", "required": ["RiskConfiguration"], "members": {"RiskConfiguration": {"shape": "S7p"}}}}, "DescribeUserImportJob": {"input": {"type": "structure", "required": ["UserPoolId", "JobId"], "members": {"UserPoolId": {}, "JobId": {}}}, "output": {"type": "structure", "members": {"UserImportJob": {"shape": "S4m"}}}}, "DescribeUserPool": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}}}, "output": {"type": "structure", "members": {"UserPool": {"shape": "S6c"}}}}, "DescribeUserPoolClient": {"input": {"type": "structure", "required": ["UserPoolId", "ClientId"], "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}}}, "output": {"type": "structure", "members": {"UserPoolClient": {"shape": "S74"}}}}, "DescribeUserPoolDomain": {"input": {"type": "structure", "required": ["Domain"], "members": {"Domain": {}}}, "output": {"type": "structure", "members": {"DomainDescription": {"type": "structure", "members": {"UserPoolId": {}, "AWSAccountId": {}, "Domain": {}, "S3Bucket": {}, "CloudFrontDistribution": {}, "Version": {}, "Status": {}, "CustomDomainConfig": {"shape": "S77"}}}}}}, "ForgetDevice": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"AccessToken": {"shape": "S1v"}, "DeviceKey": {}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "ForgotPassword": {"input": {"type": "structure", "required": ["ClientId", "Username"], "members": {"ClientId": {"shape": "S1j"}, "SecretHash": {"shape": "S3s"}, "UserContextData": {"shape": "S3u"}, "Username": {"shape": "Sd"}, "AnalyticsMetadata": {"shape": "S1m"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {"CodeDeliveryDetails": {"shape": "S8n"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "GetCSVHeader": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}}}, "output": {"type": "structure", "members": {"UserPoolId": {}, "CSVHeader": {"type": "list", "member": {}}}}}, "GetDevice": {"input": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON>"], "members": {"DeviceKey": {}, "AccessToken": {"shape": "S1v"}}}, "output": {"type": "structure", "required": ["<PERSON><PERSON>"], "members": {"Device": {"shape": "S1e"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "GetGroup": {"input": {"type": "structure", "required": ["GroupName", "UserPoolId"], "members": {"GroupName": {}, "UserPoolId": {}}}, "output": {"type": "structure", "members": {"Group": {"shape": "S29"}}}}, "GetIdentityProviderByIdentifier": {"input": {"type": "structure", "required": ["UserPoolId", "IdpIdentifier"], "members": {"UserPoolId": {}, "IdpIdentifier": {}}}, "output": {"type": "structure", "required": ["IdentityProvider"], "members": {"IdentityProvider": {"shape": "S49"}}}}, "GetLogDeliveryConfiguration": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}}}, "output": {"type": "structure", "members": {"LogDeliveryConfiguration": {"shape": "S8z"}}}}, "GetSigningCertificate": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}}}, "output": {"type": "structure", "members": {"Certificate": {}}}}, "GetUICustomization": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}}}, "output": {"type": "structure", "required": ["UICustomization"], "members": {"UICustomization": {"shape": "S9c"}}}}, "GetUser": {"input": {"type": "structure", "required": ["AccessToken"], "members": {"AccessToken": {"shape": "S1v"}}}, "output": {"type": "structure", "required": ["Username", "UserAttributes"], "members": {"Username": {"shape": "Sd"}, "UserAttributes": {"shape": "Sj"}, "MFAOptions": {"shape": "Sw"}, "PreferredMfaSetting": {}, "UserMFASettingList": {"shape": "S1h"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "GetUserAttributeVerificationCode": {"input": {"type": "structure", "required": ["AccessToken", "AttributeName"], "members": {"AccessToken": {"shape": "S1v"}, "AttributeName": {}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {"CodeDeliveryDetails": {"shape": "S8n"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "GetUserPoolMfaConfig": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}}}, "output": {"type": "structure", "members": {"SmsMfaConfiguration": {"shape": "S9m"}, "SoftwareTokenMfaConfiguration": {"shape": "S9n"}, "MfaConfiguration": {}}}}, "GlobalSignOut": {"input": {"type": "structure", "required": ["AccessToken"], "members": {"AccessToken": {"shape": "S1v"}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "InitiateAuth": {"input": {"type": "structure", "required": ["AuthFlow", "ClientId"], "members": {"AuthFlow": {}, "AuthParameters": {"shape": "S1l"}, "ClientMetadata": {"shape": "Sg"}, "ClientId": {"shape": "S1j"}, "AnalyticsMetadata": {"shape": "S1m"}, "UserContextData": {"shape": "S3u"}}}, "output": {"type": "structure", "members": {"ChallengeName": {}, "Session": {"shape": "S1s"}, "ChallengeParameters": {"shape": "S1t"}, "AuthenticationResult": {"shape": "S1u"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "ListDevices": {"input": {"type": "structure", "required": ["AccessToken"], "members": {"AccessToken": {"shape": "S1v"}, "Limit": {"type": "integer"}, "PaginationToken": {}}}, "output": {"type": "structure", "members": {"Devices": {"shape": "S24"}, "PaginationToken": {}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "ListGroups": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "Limit": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Groups": {"shape": "S28"}, "NextToken": {}}}}, "ListIdentityProviders": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "required": ["Providers"], "members": {"Providers": {"type": "list", "member": {"type": "structure", "members": {"ProviderName": {}, "ProviderType": {}, "LastModifiedDate": {"type": "timestamp"}, "CreationDate": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListResourceServers": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "required": ["ResourceServers"], "members": {"ResourceServers": {"type": "list", "member": {"shape": "S4i"}}, "NextToken": {}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["ResourceArn"], "members": {"ResourceArn": {}}}, "output": {"type": "structure", "members": {"Tags": {"shape": "S5u"}}}}, "ListUserImportJobs": {"input": {"type": "structure", "required": ["UserPoolId", "MaxResults"], "members": {"UserPoolId": {}, "MaxResults": {"type": "integer"}, "PaginationToken": {}}}, "output": {"type": "structure", "members": {"UserImportJobs": {"type": "list", "member": {"shape": "S4m"}}, "PaginationToken": {}}}}, "ListUserPoolClients": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "MaxResults": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"UserPoolClients": {"type": "list", "member": {"type": "structure", "members": {"ClientId": {"shape": "S1j"}, "UserPoolId": {}, "ClientName": {}}}}, "NextToken": {}}}}, "ListUserPools": {"input": {"type": "structure", "required": ["MaxResults"], "members": {"NextToken": {}, "MaxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"UserPools": {"type": "list", "member": {"type": "structure", "members": {"Id": {}, "Name": {}, "LambdaConfig": {"shape": "S50"}, "Status": {"deprecated": true, "deprecatedMessage": "This property is no longer available."}, "LastModifiedDate": {"type": "timestamp"}, "CreationDate": {"type": "timestamp"}}}}, "NextToken": {}}}}, "ListUsers": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "AttributesToGet": {"type": "list", "member": {}}, "Limit": {"type": "integer"}, "PaginationToken": {}, "Filter": {}}}, "output": {"type": "structure", "members": {"Users": {"shape": "<PERSON>p"}, "PaginationToken": {}}}}, "ListUsersInGroup": {"input": {"type": "structure", "required": ["UserPoolId", "GroupName"], "members": {"UserPoolId": {}, "GroupName": {}, "Limit": {"type": "integer"}, "NextToken": {}}}, "output": {"type": "structure", "members": {"Users": {"shape": "<PERSON>p"}, "NextToken": {}}}}, "ResendConfirmationCode": {"input": {"type": "structure", "required": ["ClientId", "Username"], "members": {"ClientId": {"shape": "S1j"}, "SecretHash": {"shape": "S3s"}, "UserContextData": {"shape": "S3u"}, "Username": {"shape": "Sd"}, "AnalyticsMetadata": {"shape": "S1m"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {"CodeDeliveryDetails": {"shape": "S8n"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "RespondToAuthChallenge": {"input": {"type": "structure", "required": ["ClientId", "ChallengeName"], "members": {"ClientId": {"shape": "S1j"}, "ChallengeName": {}, "Session": {"shape": "S1s"}, "ChallengeResponses": {"shape": "S2y"}, "AnalyticsMetadata": {"shape": "S1m"}, "UserContextData": {"shape": "S3u"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {"ChallengeName": {}, "Session": {"shape": "S1s"}, "ChallengeParameters": {"shape": "S1t"}, "AuthenticationResult": {"shape": "S1u"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "RevokeToken": {"input": {"type": "structure", "required": ["Token", "ClientId"], "members": {"Token": {"shape": "S1v"}, "ClientId": {"shape": "S1j"}, "ClientSecret": {"shape": "S75"}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "SetLogDeliveryConfiguration": {"input": {"type": "structure", "required": ["UserPoolId", "LogConfigurations"], "members": {"UserPoolId": {}, "LogConfigurations": {"shape": "S90"}}}, "output": {"type": "structure", "members": {"LogDeliveryConfiguration": {"shape": "S8z"}}}}, "SetRiskConfiguration": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}, "CompromisedCredentialsRiskConfiguration": {"shape": "S7q"}, "AccountTakeoverRiskConfiguration": {"shape": "S7v"}, "RiskExceptionConfiguration": {"shape": "S84"}}}, "output": {"type": "structure", "required": ["RiskConfiguration"], "members": {"RiskConfiguration": {"shape": "S7p"}}}}, "SetUICustomization": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}, "CSS": {}, "ImageFile": {"type": "blob"}}}, "output": {"type": "structure", "required": ["UICustomization"], "members": {"UICustomization": {"shape": "S9c"}}}}, "SetUserMFAPreference": {"input": {"type": "structure", "required": ["AccessToken"], "members": {"SMSMfaSettings": {"shape": "S31"}, "SoftwareTokenMfaSettings": {"shape": "S32"}, "AccessToken": {"shape": "S1v"}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "SetUserPoolMfaConfig": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "SmsMfaConfiguration": {"shape": "S9m"}, "SoftwareTokenMfaConfiguration": {"shape": "S9n"}, "MfaConfiguration": {}}}, "output": {"type": "structure", "members": {"SmsMfaConfiguration": {"shape": "S9m"}, "SoftwareTokenMfaConfiguration": {"shape": "S9n"}, "MfaConfiguration": {}}}}, "SetUserSettings": {"input": {"type": "structure", "required": ["AccessToken", "MFAOptions"], "members": {"AccessToken": {"shape": "S1v"}, "MFAOptions": {"shape": "Sw"}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "SignUp": {"input": {"type": "structure", "required": ["ClientId", "Username", "Password"], "members": {"ClientId": {"shape": "S1j"}, "SecretHash": {"shape": "S3s"}, "Username": {"shape": "Sd"}, "Password": {"shape": "Sn"}, "UserAttributes": {"shape": "Sj"}, "ValidationData": {"shape": "Sj"}, "AnalyticsMetadata": {"shape": "S1m"}, "UserContextData": {"shape": "S3u"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "required": ["UserConfirmed", "UserSub"], "members": {"UserConfirmed": {"type": "boolean"}, "CodeDeliveryDetails": {"shape": "S8n"}, "UserSub": {}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "StartUserImportJob": {"input": {"type": "structure", "required": ["UserPoolId", "JobId"], "members": {"UserPoolId": {}, "JobId": {}}}, "output": {"type": "structure", "members": {"UserImportJob": {"shape": "S4m"}}}}, "StopUserImportJob": {"input": {"type": "structure", "required": ["UserPoolId", "JobId"], "members": {"UserPoolId": {}, "JobId": {}}}, "output": {"type": "structure", "members": {"UserImportJob": {"shape": "S4m"}}}}, "TagResource": {"input": {"type": "structure", "required": ["ResourceArn", "Tags"], "members": {"ResourceArn": {}, "Tags": {"shape": "S5u"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"input": {"type": "structure", "required": ["ResourceArn", "TagKeys"], "members": {"ResourceArn": {}, "TagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateAuthEventFeedback": {"input": {"type": "structure", "required": ["UserPoolId", "Username", "EventId", "FeedbackToken", "Feedback<PERSON><PERSON><PERSON>"], "members": {"UserPoolId": {}, "Username": {"shape": "Sd"}, "EventId": {}, "FeedbackToken": {"shape": "S1v"}, "FeedbackValue": {}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "UpdateDeviceStatus": {"input": {"type": "structure", "required": ["AccessToken", "<PERSON><PERSON><PERSON><PERSON>"], "members": {"AccessToken": {"shape": "S1v"}, "DeviceKey": {}, "DeviceRememberedStatus": {}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "UpdateGroup": {"input": {"type": "structure", "required": ["GroupName", "UserPoolId"], "members": {"GroupName": {}, "UserPoolId": {}, "Description": {}, "RoleArn": {}, "Precedence": {"type": "integer"}}}, "output": {"type": "structure", "members": {"Group": {"shape": "S29"}}}}, "UpdateIdentityProvider": {"input": {"type": "structure", "required": ["UserPoolId", "ProviderName"], "members": {"UserPoolId": {}, "ProviderName": {}, "ProviderDetails": {"shape": "S43"}, "AttributeMapping": {"shape": "S44"}, "IdpIdentifiers": {"shape": "S46"}}}, "output": {"type": "structure", "required": ["IdentityProvider"], "members": {"IdentityProvider": {"shape": "S49"}}}}, "UpdateResourceServer": {"input": {"type": "structure", "required": ["UserPoolId", "Identifier", "Name"], "members": {"UserPoolId": {}, "Identifier": {}, "Name": {}, "Scopes": {"shape": "S4d"}}}, "output": {"type": "structure", "required": ["ResourceServer"], "members": {"ResourceServer": {"shape": "S4i"}}}}, "UpdateUserAttributes": {"input": {"type": "structure", "required": ["UserAttributes", "AccessToken"], "members": {"UserAttributes": {"shape": "Sj"}, "AccessToken": {"shape": "S1v"}, "ClientMetadata": {"shape": "Sg"}}}, "output": {"type": "structure", "members": {"CodeDeliveryDetailsList": {"type": "list", "member": {"shape": "S8n"}}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "UpdateUserPool": {"input": {"type": "structure", "required": ["UserPoolId"], "members": {"UserPoolId": {}, "Policies": {"shape": "S4u"}, "DeletionProtection": {}, "LambdaConfig": {"shape": "S50"}, "AutoVerifiedAttributes": {"shape": "S57"}, "SmsVerificationMessage": {}, "EmailVerificationMessage": {}, "EmailVerificationSubject": {}, "VerificationMessageTemplate": {"shape": "S5g"}, "SmsAuthenticationMessage": {}, "UserAttributeUpdateSettings": {"shape": "S5l"}, "MfaConfiguration": {}, "DeviceConfiguration": {"shape": "S5n"}, "EmailConfiguration": {"shape": "S5o"}, "SmsConfiguration": {"shape": "S5s"}, "UserPoolTags": {"shape": "S5u"}, "AdminCreateUserConfig": {"shape": "S5x"}, "UserPoolAddOns": {"shape": "S61"}, "AccountRecoverySetting": {"shape": "S66"}}}, "output": {"type": "structure", "members": {}}}, "UpdateUserPoolClient": {"input": {"type": "structure", "required": ["UserPoolId", "ClientId"], "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}, "ClientName": {}, "RefreshTokenValidity": {"type": "integer"}, "AccessTokenValidity": {"type": "integer"}, "IdTokenValidity": {"type": "integer"}, "TokenValidityUnits": {"shape": "S6l"}, "ReadAttributes": {"shape": "S6n"}, "WriteAttributes": {"shape": "S6n"}, "ExplicitAuthFlows": {"shape": "S6p"}, "SupportedIdentityProviders": {"shape": "S6r"}, "CallbackURLs": {"shape": "S6s"}, "LogoutURLs": {"shape": "S6u"}, "DefaultRedirectURI": {}, "AllowedOAuthFlows": {"shape": "S6v"}, "AllowedOAuthScopes": {"shape": "S6x"}, "AllowedOAuthFlowsUserPoolClient": {"type": "boolean"}, "AnalyticsConfiguration": {"shape": "S6z"}, "PreventUserExistenceErrors": {}, "EnableTokenRevocation": {"type": "boolean"}, "EnablePropagateAdditionalUserContextData": {"type": "boolean"}, "AuthSessionValidity": {"type": "integer"}}}, "output": {"type": "structure", "members": {"UserPoolClient": {"shape": "S74"}}}}, "UpdateUserPoolDomain": {"input": {"type": "structure", "required": ["Domain", "UserPoolId", "CustomDomainConfig"], "members": {"Domain": {}, "UserPoolId": {}, "CustomDomainConfig": {"shape": "S77"}}}, "output": {"type": "structure", "members": {"CloudFrontDomain": {}}}}, "VerifySoftwareToken": {"input": {"type": "structure", "required": ["UserCode"], "members": {"AccessToken": {"shape": "S1v"}, "Session": {"shape": "S1s"}, "UserCode": {"type": "string", "sensitive": true}, "FriendlyDeviceName": {}}}, "output": {"type": "structure", "members": {"Status": {}, "Session": {"shape": "S1s"}}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}, "VerifyUserAttribute": {"input": {"type": "structure", "required": ["AccessToken", "AttributeName", "Code"], "members": {"AccessToken": {"shape": "S1v"}, "AttributeName": {}, "Code": {}}}, "output": {"type": "structure", "members": {}}, "authtype": "none", "auth": ["smithy.api#noAuth"]}}, "shapes": {"S4": {"type": "structure", "members": {"Name": {}, "AttributeDataType": {}, "DeveloperOnlyAttribute": {"type": "boolean"}, "Mutable": {"type": "boolean"}, "Required": {"type": "boolean"}, "NumberAttributeConstraints": {"type": "structure", "members": {"MinValue": {}, "MaxValue": {}}}, "StringAttributeConstraints": {"type": "structure", "members": {"MinLength": {}, "MaxLength": {}}}}}, "Sd": {"type": "string", "sensitive": true}, "Sg": {"type": "map", "key": {}, "value": {}}, "Sj": {"type": "list", "member": {"type": "structure", "required": ["Name"], "members": {"Name": {}, "Value": {"type": "string", "sensitive": true}}}}, "Sn": {"type": "string", "sensitive": true}, "St": {"type": "structure", "members": {"Username": {"shape": "Sd"}, "Attributes": {"shape": "Sj"}, "UserCreateDate": {"type": "timestamp"}, "UserLastModifiedDate": {"type": "timestamp"}, "Enabled": {"type": "boolean"}, "UserStatus": {}, "MFAOptions": {"shape": "Sw"}}}, "Sw": {"type": "list", "member": {"type": "structure", "members": {"DeliveryMedium": {}, "AttributeName": {}}}}, "S10": {"type": "list", "member": {}}, "S13": {"type": "structure", "members": {"ProviderName": {}, "ProviderAttributeName": {}, "ProviderAttributeValue": {}}}, "S1e": {"type": "structure", "members": {"DeviceKey": {}, "DeviceAttributes": {"shape": "Sj"}, "DeviceCreateDate": {"type": "timestamp"}, "DeviceLastModifiedDate": {"type": "timestamp"}, "DeviceLastAuthenticatedDate": {"type": "timestamp"}}}, "S1h": {"type": "list", "member": {}}, "S1j": {"type": "string", "sensitive": true}, "S1l": {"type": "map", "key": {}, "value": {}, "sensitive": true}, "S1m": {"type": "structure", "members": {"AnalyticsEndpointId": {}}}, "S1n": {"type": "structure", "required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "ServerName", "ServerPath", "HttpHeaders"], "members": {"IpAddress": {}, "ServerName": {}, "ServerPath": {}, "HttpHeaders": {"type": "list", "member": {"type": "structure", "members": {"headerName": {}, "headerValue": {}}}}, "EncodedData": {}}}, "S1s": {"type": "string", "sensitive": true}, "S1t": {"type": "map", "key": {}, "value": {}}, "S1u": {"type": "structure", "members": {"AccessToken": {"shape": "S1v"}, "ExpiresIn": {"type": "integer"}, "TokenType": {}, "RefreshToken": {"shape": "S1v"}, "IdToken": {"shape": "S1v"}, "NewDeviceMetadata": {"type": "structure", "members": {"DeviceKey": {}, "DeviceGroupKey": {}}}}}, "S1v": {"type": "string", "sensitive": true}, "S24": {"type": "list", "member": {"shape": "S1e"}}, "S28": {"type": "list", "member": {"shape": "S29"}}, "S29": {"type": "structure", "members": {"GroupName": {}, "UserPoolId": {}, "Description": {}, "RoleArn": {}, "Precedence": {"type": "integer"}, "LastModifiedDate": {"type": "timestamp"}, "CreationDate": {"type": "timestamp"}}}, "S2y": {"type": "map", "key": {}, "value": {}, "sensitive": true}, "S31": {"type": "structure", "members": {"Enabled": {"type": "boolean"}, "PreferredMfa": {"type": "boolean"}}}, "S32": {"type": "structure", "members": {"Enabled": {"type": "boolean"}, "PreferredMfa": {"type": "boolean"}}}, "S3s": {"type": "string", "sensitive": true}, "S3u": {"type": "structure", "members": {"IpAddress": {}, "EncodedData": {}}, "sensitive": true}, "S43": {"type": "map", "key": {}, "value": {}}, "S44": {"type": "map", "key": {}, "value": {}}, "S46": {"type": "list", "member": {}}, "S49": {"type": "structure", "members": {"UserPoolId": {}, "ProviderName": {}, "ProviderType": {}, "ProviderDetails": {"shape": "S43"}, "AttributeMapping": {"shape": "S44"}, "IdpIdentifiers": {"shape": "S46"}, "LastModifiedDate": {"type": "timestamp"}, "CreationDate": {"type": "timestamp"}}}, "S4d": {"type": "list", "member": {"type": "structure", "required": ["ScopeName", "ScopeDescription"], "members": {"ScopeName": {}, "ScopeDescription": {}}}}, "S4i": {"type": "structure", "members": {"UserPoolId": {}, "Identifier": {}, "Name": {}, "Scopes": {"shape": "S4d"}}}, "S4m": {"type": "structure", "members": {"JobName": {}, "JobId": {}, "UserPoolId": {}, "PreSignedUrl": {}, "CreationDate": {"type": "timestamp"}, "StartDate": {"type": "timestamp"}, "CompletionDate": {"type": "timestamp"}, "Status": {}, "CloudWatchLogsRoleArn": {}, "ImportedUsers": {"type": "long"}, "SkippedUsers": {"type": "long"}, "FailedUsers": {"type": "long"}, "CompletionMessage": {}}}, "S4u": {"type": "structure", "members": {"PasswordPolicy": {"type": "structure", "members": {"MinimumLength": {"type": "integer"}, "RequireUppercase": {"type": "boolean"}, "RequireLowercase": {"type": "boolean"}, "RequireNumbers": {"type": "boolean"}, "RequireSymbols": {"type": "boolean"}, "PasswordHistorySize": {"type": "integer"}, "TemporaryPasswordValidityDays": {"type": "integer"}}}}}, "S50": {"type": "structure", "members": {"PreSignUp": {}, "CustomMessage": {}, "PostConfirmation": {}, "PreAuthentication": {}, "PostAuthentication": {}, "DefineAuthChallenge": {}, "CreateAuthChallenge": {}, "VerifyAuthChallengeResponse": {}, "PreTokenGeneration": {}, "UserMigration": {}, "PreTokenGenerationConfig": {"type": "structure", "required": ["LambdaVersion", "LambdaArn"], "members": {"LambdaVersion": {}, "LambdaArn": {}}}, "CustomSMSSender": {"type": "structure", "required": ["LambdaVersion", "LambdaArn"], "members": {"LambdaVersion": {}, "LambdaArn": {}}}, "CustomEmailSender": {"type": "structure", "required": ["LambdaVersion", "LambdaArn"], "members": {"LambdaVersion": {}, "LambdaArn": {}}}, "KMSKeyID": {}}}, "S57": {"type": "list", "member": {}}, "S59": {"type": "list", "member": {}}, "S5b": {"type": "list", "member": {}}, "S5g": {"type": "structure", "members": {"SmsMessage": {}, "EmailMessage": {}, "EmailSubject": {}, "EmailMessageByLink": {}, "EmailSubjectByLink": {}, "DefaultEmailOption": {}}}, "S5l": {"type": "structure", "members": {"AttributesRequireVerificationBeforeUpdate": {"type": "list", "member": {}}}}, "S5n": {"type": "structure", "members": {"ChallengeRequiredOnNewDevice": {"type": "boolean"}, "DeviceOnlyRememberedOnUserPrompt": {"type": "boolean"}}}, "S5o": {"type": "structure", "members": {"SourceArn": {}, "ReplyToEmailAddress": {}, "EmailSendingAccount": {}, "From": {}, "ConfigurationSet": {}}}, "S5s": {"type": "structure", "required": ["SnsCallerArn"], "members": {"SnsCallerArn": {}, "ExternalId": {}, "SnsRegion": {}}}, "S5u": {"type": "map", "key": {}, "value": {}}, "S5x": {"type": "structure", "members": {"AllowAdminCreateUserOnly": {"type": "boolean"}, "UnusedAccountValidityDays": {"type": "integer"}, "InviteMessageTemplate": {"type": "structure", "members": {"SMSMessage": {}, "EmailMessage": {}, "EmailSubject": {}}}}}, "S60": {"type": "list", "member": {"shape": "S4"}}, "S61": {"type": "structure", "required": ["AdvancedSecurityMode"], "members": {"AdvancedSecurityMode": {}, "AdvancedSecurityAdditionalFlows": {"type": "structure", "members": {"CustomAuthMode": {}}}}}, "S65": {"type": "structure", "required": ["CaseSensitive"], "members": {"CaseSensitive": {"type": "boolean"}}}, "S66": {"type": "structure", "members": {"RecoveryMechanisms": {"type": "list", "member": {"type": "structure", "required": ["Priority", "Name"], "members": {"Priority": {"type": "integer"}, "Name": {}}}}}}, "S6c": {"type": "structure", "members": {"Id": {}, "Name": {}, "Policies": {"shape": "S4u"}, "DeletionProtection": {}, "LambdaConfig": {"shape": "S50"}, "Status": {"deprecated": true, "deprecatedMessage": "This property is no longer available."}, "LastModifiedDate": {"type": "timestamp"}, "CreationDate": {"type": "timestamp"}, "SchemaAttributes": {"shape": "S60"}, "AutoVerifiedAttributes": {"shape": "S57"}, "AliasAttributes": {"shape": "S59"}, "UsernameAttributes": {"shape": "S5b"}, "SmsVerificationMessage": {}, "EmailVerificationMessage": {}, "EmailVerificationSubject": {}, "VerificationMessageTemplate": {"shape": "S5g"}, "SmsAuthenticationMessage": {}, "UserAttributeUpdateSettings": {"shape": "S5l"}, "MfaConfiguration": {}, "DeviceConfiguration": {"shape": "S5n"}, "EstimatedNumberOfUsers": {"type": "integer"}, "EmailConfiguration": {"shape": "S5o"}, "SmsConfiguration": {"shape": "S5s"}, "UserPoolTags": {"shape": "S5u"}, "SmsConfigurationFailure": {}, "EmailConfigurationFailure": {}, "Domain": {}, "CustomDomain": {}, "AdminCreateUserConfig": {"shape": "S5x"}, "UserPoolAddOns": {"shape": "S61"}, "UsernameConfiguration": {"shape": "S65"}, "Arn": {}, "AccountRecoverySetting": {"shape": "S66"}}}, "S6l": {"type": "structure", "members": {"AccessToken": {}, "IdToken": {}, "RefreshToken": {}}}, "S6n": {"type": "list", "member": {}}, "S6p": {"type": "list", "member": {}}, "S6r": {"type": "list", "member": {}}, "S6s": {"type": "list", "member": {}}, "S6u": {"type": "list", "member": {}}, "S6v": {"type": "list", "member": {}}, "S6x": {"type": "list", "member": {}}, "S6z": {"type": "structure", "members": {"ApplicationId": {}, "ApplicationArn": {}, "RoleArn": {}, "ExternalId": {}, "UserDataShared": {"type": "boolean"}}}, "S74": {"type": "structure", "members": {"UserPoolId": {}, "ClientName": {}, "ClientId": {"shape": "S1j"}, "ClientSecret": {"shape": "S75"}, "LastModifiedDate": {"type": "timestamp"}, "CreationDate": {"type": "timestamp"}, "RefreshTokenValidity": {"type": "integer"}, "AccessTokenValidity": {"type": "integer"}, "IdTokenValidity": {"type": "integer"}, "TokenValidityUnits": {"shape": "S6l"}, "ReadAttributes": {"shape": "S6n"}, "WriteAttributes": {"shape": "S6n"}, "ExplicitAuthFlows": {"shape": "S6p"}, "SupportedIdentityProviders": {"shape": "S6r"}, "CallbackURLs": {"shape": "S6s"}, "LogoutURLs": {"shape": "S6u"}, "DefaultRedirectURI": {}, "AllowedOAuthFlows": {"shape": "S6v"}, "AllowedOAuthScopes": {"shape": "S6x"}, "AllowedOAuthFlowsUserPoolClient": {"type": "boolean"}, "AnalyticsConfiguration": {"shape": "S6z"}, "PreventUserExistenceErrors": {}, "EnableTokenRevocation": {"type": "boolean"}, "EnablePropagateAdditionalUserContextData": {"type": "boolean"}, "AuthSessionValidity": {"type": "integer"}}}, "S75": {"type": "string", "sensitive": true}, "S77": {"type": "structure", "required": ["CertificateArn"], "members": {"CertificateArn": {}}}, "S7p": {"type": "structure", "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}, "CompromisedCredentialsRiskConfiguration": {"shape": "S7q"}, "AccountTakeoverRiskConfiguration": {"shape": "S7v"}, "RiskExceptionConfiguration": {"shape": "S84"}, "LastModifiedDate": {"type": "timestamp"}}}, "S7q": {"type": "structure", "required": ["Actions"], "members": {"EventFilter": {"type": "list", "member": {}}, "Actions": {"type": "structure", "required": ["EventAction"], "members": {"EventAction": {}}}}}, "S7v": {"type": "structure", "required": ["Actions"], "members": {"NotifyConfiguration": {"type": "structure", "required": ["SourceArn"], "members": {"From": {}, "ReplyTo": {}, "SourceArn": {}, "BlockEmail": {"shape": "S7x"}, "NoActionEmail": {"shape": "S7x"}, "MfaEmail": {"shape": "S7x"}}}, "Actions": {"type": "structure", "members": {"LowAction": {"shape": "S81"}, "MediumAction": {"shape": "S81"}, "HighAction": {"shape": "S81"}}}}}, "S7x": {"type": "structure", "required": ["Subject"], "members": {"Subject": {}, "HtmlBody": {}, "TextBody": {}}}, "S81": {"type": "structure", "required": ["Notify", "EventAction"], "members": {"Notify": {"type": "boolean"}, "EventAction": {}}}, "S84": {"type": "structure", "members": {"BlockedIPRangeList": {"type": "list", "member": {}}, "SkippedIPRangeList": {"type": "list", "member": {}}}}, "S8n": {"type": "structure", "members": {"Destination": {}, "DeliveryMedium": {}, "AttributeName": {}}}, "S8z": {"type": "structure", "required": ["UserPoolId", "LogConfigurations"], "members": {"UserPoolId": {}, "LogConfigurations": {"shape": "S90"}}}, "S90": {"type": "list", "member": {"type": "structure", "required": ["LogLevel", "EventSource"], "members": {"LogLevel": {}, "EventSource": {}, "CloudWatchLogsConfiguration": {"type": "structure", "members": {"LogGroupArn": {}}}, "S3Configuration": {"type": "structure", "members": {"BucketArn": {}}}, "FirehoseConfiguration": {"type": "structure", "members": {"StreamArn": {}}}}}}, "S9c": {"type": "structure", "members": {"UserPoolId": {}, "ClientId": {"shape": "S1j"}, "ImageUrl": {}, "CSS": {}, "CSSVersion": {}, "LastModifiedDate": {"type": "timestamp"}, "CreationDate": {"type": "timestamp"}}}, "S9m": {"type": "structure", "members": {"SmsAuthenticationMessage": {}, "SmsConfiguration": {"shape": "S5s"}}}, "S9n": {"type": "structure", "members": {"Enabled": {"type": "boolean"}}}, "Sap": {"type": "list", "member": {"shape": "St"}}}}