{"version": "2.0", "metadata": {"apiVersion": "2018-05-10", "endpointPrefix": "datazone", "jsonVersion": "1.1", "protocol": "rest-json", "serviceFullName": "Amazon DataZone", "serviceId": "DataZone", "signatureVersion": "v4", "signingName": "datazone", "uid": "datazone-2018-05-10"}, "operations": {"AcceptPredictions": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}/accept-predictions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"acceptChoices": {"type": "list", "member": {"type": "structure", "required": ["predictionTarget"], "members": {"editedValue": {"type": "string", "sensitive": true}, "predictionChoice": {"type": "integer"}, "predictionTarget": {}}}}, "acceptRule": {"type": "structure", "members": {"rule": {}, "threshold": {"type": "float"}}}, "clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["assetId", "domainId", "revision"], "members": {"assetId": {}, "domainId": {}, "revision": {}}}, "idempotent": true}, "AcceptSubscriptionRequest": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests/{identifier}/accept", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"assetScopes": {"type": "list", "member": {"type": "structure", "required": ["assetId", "filterIds"], "members": {"assetId": {}, "filterIds": {"shape": "Sj"}}}}, "decisionComment": {"shape": "Sl"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sl"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sq"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "St"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S1b"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "AddEntityOwner": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/addOwner", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "owner"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "owner": {"shape": "S1i"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "AddPolicyGrant": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/policies/managed/{entityType}/{entityIdentifier}/addGrant", "responseCode": 201}, "input": {"type": "structure", "required": ["detail", "domainIdentifier", "entityIdentifier", "entityType", "policyType", "principal"], "members": {"clientToken": {"idempotencyToken": true}, "detail": {"shape": "S1p"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "policyType": {}, "principal": {"shape": "S24"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "AssociateEnvironmentRole": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/roles/{environmentRoleArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "environmentRoleArn"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "environmentRoleArn": {"location": "uri", "locationName": "environmentRoleArn"}}}, "output": {"type": "structure", "members": {}}}, "CancelMetadataGenerationRun": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/metadata-generation-runs/{identifier}/cancel", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}}, "CancelSubscription": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/subscriptions/{identifier}/cancel", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "status", "subscribedListing", "subscribedPrincipal", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "retainPermissions": {"type": "boolean"}, "status": {}, "subscribedListing": {"shape": "St"}, "subscribedPrincipal": {"shape": "S1b"}, "subscriptionRequestId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "CreateAsset": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/assets", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "name", "owningProjectIdentifier", "typeIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "externalIdentifier": {"shape": "S2s"}, "formsInput": {"shape": "S2t"}, "glossaryTerms": {"shape": "S2y"}, "name": {"shape": "S30"}, "owningProjectIdentifier": {}, "predictionConfiguration": {"shape": "S31"}, "typeIdentifier": {}, "typeRevision": {}}}, "output": {"type": "structure", "required": ["domainId", "formsOutput", "id", "name", "owningProjectId", "revision", "typeIdentifier", "typeRevision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "externalIdentifier": {"shape": "S2s"}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S35"}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "latestTimeSeriesDataPointFormsOutput": {"shape": "S38"}, "listing": {"shape": "S3e"}, "name": {"shape": "S30"}, "owningProjectId": {}, "predictionConfiguration": {"shape": "S31"}, "readOnlyFormsOutput": {"shape": "S35"}, "revision": {}, "typeIdentifier": {}, "typeRevision": {}}}, "idempotent": true}, "CreateAssetFilter": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/assets/{assetIdentifier}/filters", "responseCode": 201}, "input": {"type": "structure", "required": ["assetIdentifier", "configuration", "domainIdentifier", "name"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "clientToken": {"idempotencyToken": true}, "configuration": {"shape": "S3h"}, "description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "name": {"shape": "S41"}}}, "output": {"type": "structure", "required": ["assetId", "configuration", "domainId", "id", "name"], "members": {"assetId": {}, "configuration": {"shape": "S3h"}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Su"}, "domainId": {}, "effectiveColumnNames": {"shape": "S3j"}, "effectiveRowFilter": {}, "errorMessage": {}, "id": {}, "name": {"shape": "S41"}, "status": {}}}, "idempotent": true}, "CreateAssetRevision": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}/revisions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier", "name"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formsInput": {"shape": "S2t"}, "glossaryTerms": {"shape": "S2y"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S30"}, "predictionConfiguration": {"shape": "S31"}, "typeRevision": {}}}, "output": {"type": "structure", "required": ["domainId", "formsOutput", "id", "name", "owningProjectId", "revision", "typeIdentifier", "typeRevision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "externalIdentifier": {"shape": "S2s"}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S35"}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "latestTimeSeriesDataPointFormsOutput": {"shape": "S38"}, "listing": {"shape": "S3e"}, "name": {"shape": "S30"}, "owningProjectId": {}, "predictionConfiguration": {"shape": "S31"}, "readOnlyFormsOutput": {"shape": "S35"}, "revision": {}, "typeIdentifier": {}, "typeRevision": {}}}, "idempotent": true}, "CreateAssetType": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/asset-types", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "formsInput", "name", "owningProjectIdentifier"], "members": {"description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formsInput": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["typeIdentifier", "typeRevision"], "members": {"required": {"type": "boolean"}, "typeIdentifier": {}, "typeRevision": {}}}}, "name": {}, "owningProjectIdentifier": {}}}, "output": {"type": "structure", "required": ["domainId", "formsOutput", "name", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "formsOutput": {"shape": "S4a"}, "name": {}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "CreateDataProduct": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/data-products", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "name", "owningProjectIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "S4d"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formsInput": {"shape": "S2t"}, "glossaryTerms": {"shape": "S2y"}, "items": {"shape": "S4e"}, "name": {"shape": "S4j"}, "owningProjectIdentifier": {}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId", "revision", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S4d"}, "domainId": {}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S35"}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "items": {"shape": "S4e"}, "name": {"shape": "S4j"}, "owningProjectId": {}, "revision": {}, "status": {}}}, "idempotent": true}, "CreateDataProductRevision": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/data-products/{identifier}/revisions", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier", "name"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "S4d"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formsInput": {"shape": "S2t"}, "glossaryTerms": {"shape": "S2y"}, "identifier": {"location": "uri", "locationName": "identifier"}, "items": {"shape": "S4e"}, "name": {"shape": "S4j"}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId", "revision", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S4d"}, "domainId": {}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S35"}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "items": {"shape": "S4e"}, "name": {"shape": "S4j"}, "owningProjectId": {}, "revision": {}, "status": {}}}, "idempotent": true}, "CreateDataSource": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/data-sources", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "name", "projectIdentifier", "type"], "members": {"assetFormsInput": {"shape": "S2t"}, "clientToken": {"idempotencyToken": true}, "configuration": {"shape": "S4q"}, "description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "enableSetting": {}, "environmentIdentifier": {}, "name": {"shape": "S5b"}, "projectIdentifier": {}, "publishOnImport": {"type": "boolean"}, "recommendation": {"shape": "S5c"}, "schedule": {"shape": "S5d"}, "type": {}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "projectId"], "members": {"assetFormsOutput": {"shape": "S35"}, "configuration": {"shape": "S5i"}, "createdAt": {"shape": "S5r"}, "description": {"shape": "Su"}, "domainId": {}, "enableSetting": {}, "environmentId": {}, "errorMessage": {"shape": "S5s"}, "id": {}, "lastRunAt": {"shape": "S5r"}, "lastRunErrorMessage": {"shape": "S5s"}, "lastRunStatus": {}, "name": {"shape": "S5b"}, "projectId": {}, "publishOnImport": {"type": "boolean"}, "recommendation": {"shape": "S5c"}, "schedule": {"shape": "S5d"}, "status": {}, "type": {}, "updatedAt": {"shape": "S5r"}}}, "idempotent": true}, "CreateDomain": {"http": {"requestUri": "/v2/domains", "responseCode": 201}, "input": {"type": "structure", "required": ["domainExecutionRole", "name"], "members": {"clientToken": {"idempotencyToken": true}, "description": {}, "domainExecutionRole": {}, "kmsKeyIdentifier": {}, "name": {}, "singleSignOn": {"shape": "S60"}, "tags": {"shape": "S63"}}}, "output": {"type": "structure", "required": ["id"], "members": {"arn": {}, "description": {}, "domainExecutionRole": {}, "id": {}, "kmsKeyIdentifier": {}, "name": {}, "portalUrl": {}, "rootDomainUnitId": {}, "singleSignOn": {"shape": "S60"}, "status": {}, "tags": {"shape": "S63"}}}, "idempotent": true}, "CreateDomainUnit": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/domain-units", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "name", "parentDomainUnitIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "S69"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "name": {"shape": "S6a"}, "parentDomainUnitIdentifier": {}}}, "output": {"type": "structure", "required": ["ancestorDomainUnitIds", "domainId", "id", "name", "owners"], "members": {"ancestorDomainUnitIds": {"type": "list", "member": {}}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S69"}, "domainId": {}, "id": {}, "name": {"shape": "S6a"}, "owners": {"shape": "S6d"}, "parentDomainUnitId": {}}}, "idempotent": true}, "CreateEnvironment": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/environments", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentProfileIdentifier", "name", "projectIdentifier"], "members": {"description": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentAccountIdentifier": {}, "environmentAccountRegion": {}, "environmentBlueprintIdentifier": {}, "environmentProfileIdentifier": {}, "glossaryTerms": {"shape": "S2y"}, "name": {}, "projectIdentifier": {}, "userParameters": {"shape": "S6j"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "name", "projectId", "provider"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S6o"}, "createdBy": {}, "deploymentProperties": {"shape": "S6p"}, "description": {"shape": "Su"}, "domainId": {}, "environmentActions": {"shape": "S6s"}, "environmentBlueprintId": {}, "environmentProfileId": {}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "lastDeployment": {"shape": "S6y"}, "name": {"shape": "S74"}, "projectId": {}, "provider": {}, "provisionedResources": {"shape": "S75"}, "provisioningProperties": {"shape": "S77"}, "status": {}, "updatedAt": {"shape": "S6o"}, "userParameters": {"shape": "S7a"}}}}, "CreateEnvironmentAction": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/actions", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "name", "parameters"], "members": {"description": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "name": {}, "parameters": {"shape": "S7e"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "parameters"], "members": {"description": {}, "domainId": {}, "environmentId": {}, "id": {}, "name": {}, "parameters": {"shape": "S7e"}}}}, "CreateEnvironmentProfile": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/environment-profiles", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentBlueprintIdentifier", "name", "projectIdentifier"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentBlueprintIdentifier": {}, "name": {"shape": "S7j"}, "projectIdentifier": {}, "userParameters": {"shape": "S6j"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "environmentBlueprintId", "id", "name"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S6o"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "environmentBlueprintId": {}, "id": {}, "name": {"shape": "S7j"}, "projectId": {}, "updatedAt": {"shape": "S6o"}, "userParameters": {"shape": "S7a"}}}}, "CreateFormType": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/form-types", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "model", "name", "owningProjectIdentifier"], "members": {"description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "model": {"shape": "S7m"}, "name": {"shape": "S37"}, "owningProjectIdentifier": {}, "status": {}}}, "output": {"type": "structure", "required": ["domainId", "name", "revision"], "members": {"description": {"shape": "Su"}, "domainId": {}, "name": {"shape": "S37"}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}}}}, "CreateGlossary": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/glossaries", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "name", "owningProjectIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "S7r"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "name": {"shape": "S7s"}, "owningProjectIdentifier": {}, "status": {}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId"], "members": {"description": {"shape": "S7r"}, "domainId": {}, "id": {}, "name": {"shape": "S7s"}, "owningProjectId": {}, "status": {}}}, "idempotent": true}, "CreateGlossaryTerm": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/glossary-terms", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "glossaryIdentifier", "name"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "glossaryIdentifier": {}, "longDescription": {"shape": "S7x"}, "name": {"shape": "S13"}, "shortDescription": {"shape": "S14"}, "status": {}, "termRelations": {"shape": "S7z"}}}, "output": {"type": "structure", "required": ["domainId", "glossaryId", "id", "name", "status"], "members": {"domainId": {}, "glossaryId": {}, "id": {}, "longDescription": {"shape": "S7x"}, "name": {"shape": "S13"}, "shortDescription": {"shape": "S14"}, "status": {}, "termRelations": {"shape": "S7z"}}}, "idempotent": true}, "CreateGroupProfile": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/group-profiles", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "groupIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "groupIdentifier": {}}}, "output": {"type": "structure", "members": {"domainId": {}, "groupName": {"shape": "S85"}, "id": {}, "status": {}}}, "idempotent": true}, "CreateListingChangeSet": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/listings/change-set", "responseCode": 200}, "input": {"type": "structure", "required": ["action", "domainIdentifier", "entityIdentifier", "entityType"], "members": {"action": {}, "clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {}, "entityRevision": {}, "entityType": {}}}, "output": {"type": "structure", "required": ["listingId", "listingRevision", "status"], "members": {"listingId": {}, "listingRevision": {}, "status": {}}}}, "CreateProject": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/projects", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "name"], "members": {"description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "domainUnitId": {}, "glossaryTerms": {"shape": "S2y"}, "name": {"shape": "S1d"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "id", "name"], "members": {"createdAt": {"shape": "S6o"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "domainUnitId": {}, "failureReasons": {"shape": "S8e"}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "lastUpdatedAt": {"shape": "S6o"}, "name": {"shape": "S1d"}, "projectStatus": {}}}}, "CreateProjectMembership": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/projects/{projectIdentifier}/createMembership", "responseCode": 201}, "input": {"type": "structure", "required": ["designation", "domainIdentifier", "member", "projectIdentifier"], "members": {"designation": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "member": {"shape": "S8j"}, "projectIdentifier": {"location": "uri", "locationName": "projectIdentifier"}}}, "output": {"type": "structure", "members": {}}}, "CreateSubscriptionGrant": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/subscription-grants", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "grantedEntity", "subscriptionTargetIdentifier"], "members": {"assetTargetNames": {"type": "list", "member": {"type": "structure", "required": ["assetId", "targetName"], "members": {"assetId": {}, "targetName": {}}}}, "clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {}, "grantedEntity": {"type": "structure", "members": {"listing": {"type": "structure", "required": ["identifier", "revision"], "members": {"identifier": {}, "revision": {}}}}, "union": true}, "subscriptionTargetIdentifier": {}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "grantedEntity", "id", "status", "subscriptionTargetId", "updatedAt"], "members": {"assets": {"shape": "S8s"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "grantedEntity": {"shape": "S8w"}, "id": {}, "status": {}, "subscriptionId": {"deprecated": true, "deprecatedMessage": "Multiple subscriptions can exist for a single grant"}, "subscriptionTargetId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "CreateSubscriptionRequest": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/subscription-requests", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "requestReason", "subscribedListings", "subscribedPrincipals"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "requestReason": {"shape": "Sq"}, "subscribedListings": {"type": "list", "member": {"type": "structure", "required": ["identifier"], "members": {"identifier": {}}}}, "subscribedPrincipals": {"type": "list", "member": {"type": "structure", "members": {"project": {"type": "structure", "members": {"identifier": {}}}}, "union": true}}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sl"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sq"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "St"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S1b"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "CreateSubscriptionTarget": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets", "responseCode": 200}, "input": {"type": "structure", "required": ["applicableAssetTypes", "authorizedPrincipals", "domainIdentifier", "environmentIdentifier", "manageAccessRole", "name", "subscriptionTargetConfig", "type"], "members": {"applicableAssetTypes": {"shape": "S9a"}, "authorizedPrincipals": {"shape": "S9b"}, "clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "manageAccessRole": {}, "name": {"shape": "S9d"}, "provider": {}, "subscriptionTargetConfig": {"shape": "S9e"}, "type": {}}}, "output": {"type": "structure", "required": ["applicableAssetTypes", "authorizedPrincipals", "createdAt", "created<PERSON>y", "domainId", "environmentId", "id", "manageAccessRole", "name", "projectId", "provider", "subscriptionTargetConfig", "type"], "members": {"applicableAssetTypes": {"shape": "S9a"}, "authorizedPrincipals": {"shape": "S9b"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "environmentId": {}, "id": {}, "manageAccessRole": {}, "name": {"shape": "S9d"}, "projectId": {}, "provider": {}, "subscriptionTargetConfig": {"shape": "S9e"}, "type": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "CreateUserProfile": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/user-profiles", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "userIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "userIdentifier": {}, "userType": {}}}, "output": {"type": "structure", "members": {"details": {"shape": "S9k"}, "domainId": {}, "id": {}, "status": {}, "type": {}}}, "idempotent": true}, "DeleteAsset": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteAssetFilter": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/assets/{assetIdentifier}/filters/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["assetIdentifier", "domainIdentifier", "identifier"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "idempotent": true}, "DeleteAssetType": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/asset-types/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}}, "DeleteDataProduct": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/data-products/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteDataSource": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/data-sources/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"clientToken": {"idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "retainPermissionsOnRevokeFailure": {"location": "querystring", "locationName": "retainPermissionsOnRevokeFailure", "type": "boolean"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "projectId"], "members": {"assetFormsOutput": {"shape": "S35"}, "configuration": {"shape": "S5i"}, "createdAt": {"shape": "S5r"}, "description": {"shape": "Su"}, "domainId": {}, "enableSetting": {}, "environmentId": {}, "errorMessage": {"shape": "S5s"}, "id": {}, "lastRunAt": {"shape": "S5r"}, "lastRunErrorMessage": {"shape": "S5s"}, "lastRunStatus": {}, "name": {"shape": "S5b"}, "projectId": {}, "publishOnImport": {"type": "boolean"}, "retainPermissionsOnRevokeFailure": {"type": "boolean"}, "schedule": {"shape": "S5d"}, "selfGrantStatus": {"shape": "Sa2"}, "status": {}, "type": {}, "updatedAt": {"shape": "S5r"}}}, "idempotent": true}, "DeleteDomain": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{identifier}", "responseCode": 202}, "input": {"type": "structure", "required": ["identifier"], "members": {"clientToken": {"idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "identifier": {"location": "uri", "locationName": "identifier"}, "skipDeletionCheck": {"location": "querystring", "locationName": "skipDeletionCheck", "type": "boolean"}}}, "output": {"type": "structure", "required": ["status"], "members": {"status": {}}}, "idempotent": true}, "DeleteDomainUnit": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/domain-units/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteEnvironment": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environments/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "idempotent": true}, "DeleteEnvironmentAction": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/actions/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "idempotent": true}, "DeleteEnvironmentBlueprintConfiguration": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprint-configurations/{environmentBlueprintIdentifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentBlueprintIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentBlueprintIdentifier": {"location": "uri", "locationName": "environmentBlueprintIdentifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteEnvironmentProfile": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environment-profiles/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "idempotent": true}, "DeleteFormType": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/form-types/{formTypeIdentifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "formTypeIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formTypeIdentifier": {"location": "uri", "locationName": "formTypeIdentifier"}}}, "output": {"type": "structure", "members": {}}}, "DeleteGlossary": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/glossaries/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteGlossaryTerm": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/glossary-terms/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteListing": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/listings/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteProject": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/projects/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "skipDeletionCheck": {"location": "querystring", "locationName": "skipDeletionCheck", "type": "boolean"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteProjectMembership": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/projects/{projectIdentifier}/deleteMembership", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "member", "projectIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "member": {"shape": "S8j"}, "projectIdentifier": {"location": "uri", "locationName": "projectIdentifier"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DeleteSubscriptionGrant": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/subscription-grants/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "grantedEntity", "id", "status", "subscriptionTargetId", "updatedAt"], "members": {"assets": {"shape": "S8s"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "grantedEntity": {"shape": "S8w"}, "id": {}, "status": {}, "subscriptionId": {"deprecated": true, "deprecatedMessage": "Multiple subscriptions can exist for a single grant"}, "subscriptionTargetId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "DeleteSubscriptionRequest": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}}, "DeleteSubscriptionTarget": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets/{identifier}", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}}, "DeleteTimeSeriesDataPoints": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/time-series-data-points", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "formName"], "members": {"clientToken": {"idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "formName": {"location": "querystring", "locationName": "formName"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "DisassociateEnvironmentRole": {"http": {"method": "DELETE", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/roles/{environmentRoleArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "environmentRoleArn"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "environmentRoleArn": {"location": "uri", "locationName": "environmentRoleArn"}}}, "output": {"type": "structure", "members": {}}}, "GetAsset": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["domainId", "formsOutput", "id", "name", "owningProjectId", "revision", "typeIdentifier", "typeRevision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "externalIdentifier": {"shape": "S2s"}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S35"}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "latestTimeSeriesDataPointFormsOutput": {"shape": "S38"}, "listing": {"shape": "S3e"}, "name": {"shape": "S30"}, "owningProjectId": {}, "readOnlyFormsOutput": {"shape": "S35"}, "revision": {}, "typeIdentifier": {}, "typeRevision": {}}}}, "GetAssetFilter": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/assets/{assetIdentifier}/filters/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["assetIdentifier", "domainIdentifier", "identifier"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["assetId", "configuration", "domainId", "id", "name"], "members": {"assetId": {}, "configuration": {"shape": "S3h"}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Su"}, "domainId": {}, "effectiveColumnNames": {"shape": "S3j"}, "effectiveRowFilter": {}, "errorMessage": {}, "id": {}, "name": {"shape": "S41"}, "status": {}}}}, "GetAssetType": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/asset-types/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["domainId", "formsOutput", "name", "owningProjectId", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "formsOutput": {"shape": "S4a"}, "name": {}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetDataProduct": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-products/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId", "revision", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S4d"}, "domainId": {}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "formsOutput": {"shape": "S35"}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "items": {"shape": "S4e"}, "name": {"shape": "S4j"}, "owningProjectId": {}, "revision": {}, "status": {}}}}, "GetDataSource": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-sources/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "projectId"], "members": {"assetFormsOutput": {"shape": "S35"}, "configuration": {"shape": "S5i"}, "createdAt": {"shape": "S5r"}, "description": {"shape": "Su"}, "domainId": {}, "enableSetting": {}, "environmentId": {}, "errorMessage": {"shape": "S5s"}, "id": {}, "lastRunAssetCount": {"type": "integer"}, "lastRunAt": {"shape": "S5r"}, "lastRunErrorMessage": {"shape": "S5s"}, "lastRunStatus": {}, "name": {"shape": "S5b"}, "projectId": {}, "publishOnImport": {"type": "boolean"}, "recommendation": {"shape": "S5c"}, "schedule": {"shape": "S5d"}, "selfGrantStatus": {"shape": "Sa2"}, "status": {}, "type": {}, "updatedAt": {"shape": "S5r"}}}}, "GetDataSourceRun": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-source-runs/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "dataSourceId", "domainId", "id", "projectId", "status", "type", "updatedAt"], "members": {"createdAt": {"shape": "S5r"}, "dataSourceConfigurationSnapshot": {}, "dataSourceId": {}, "domainId": {}, "errorMessage": {"shape": "S5s"}, "id": {}, "projectId": {}, "runStatisticsForAssets": {"shape": "Sbh"}, "startedAt": {"shape": "S5r"}, "status": {}, "stoppedAt": {"shape": "S5r"}, "type": {}, "updatedAt": {"shape": "S5r"}}}}, "GetDomain": {"http": {"method": "GET", "requestUri": "/v2/domains/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["identifier"], "members": {"identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainExecutionRole", "id", "status"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {}, "domainExecutionRole": {}, "id": {}, "kmsKeyIdentifier": {}, "lastUpdatedAt": {"type": "timestamp"}, "name": {}, "portalUrl": {}, "rootDomainUnitId": {}, "singleSignOn": {"shape": "S60"}, "status": {}, "tags": {"shape": "S63"}}}}, "GetDomainUnit": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/domain-units/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owners"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S69"}, "domainId": {}, "id": {}, "lastUpdatedAt": {"type": "timestamp"}, "lastUpdatedBy": {}, "name": {"shape": "S6a"}, "owners": {"shape": "S6d"}, "parentDomainUnitId": {}}}}, "GetEnvironment": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "name", "projectId", "provider"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S6o"}, "createdBy": {}, "deploymentProperties": {"shape": "S6p"}, "description": {"shape": "Su"}, "domainId": {}, "environmentActions": {"shape": "S6s"}, "environmentBlueprintId": {}, "environmentProfileId": {}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "lastDeployment": {"shape": "S6y"}, "name": {"shape": "S74"}, "projectId": {}, "provider": {}, "provisionedResources": {"shape": "S75"}, "provisioningProperties": {"shape": "S77"}, "status": {}, "updatedAt": {"shape": "S6o"}, "userParameters": {"shape": "S7a"}}}}, "GetEnvironmentAction": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/actions/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "parameters"], "members": {"description": {}, "domainId": {}, "environmentId": {}, "id": {}, "name": {}, "parameters": {"shape": "S7e"}}}}, "GetEnvironmentBlueprint": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprints/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["id", "name", "provider", "provisioningProperties"], "members": {"createdAt": {"shape": "S6o"}, "deploymentProperties": {"shape": "S6p"}, "description": {"shape": "Su"}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "name": {}, "provider": {}, "provisioningProperties": {"shape": "S77"}, "updatedAt": {"shape": "S6o"}, "userParameters": {"shape": "S7a"}}}}, "GetEnvironmentBlueprintConfiguration": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprint-configurations/{environmentBlueprintIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentBlueprintIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentBlueprintIdentifier": {"location": "uri", "locationName": "environmentBlueprintIdentifier"}}}, "output": {"type": "structure", "required": ["domainId", "environmentBlueprintId"], "members": {"createdAt": {"shape": "S6o"}, "domainId": {}, "enabledRegions": {"shape": "Sbw"}, "environmentBlueprintId": {}, "manageAccessRoleArn": {}, "provisioningConfigurations": {"shape": "Sby"}, "provisioningRoleArn": {}, "regionalParameters": {"shape": "Sc3"}, "updatedAt": {"shape": "S6o"}}}}, "GetEnvironmentCredentials": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/credentials", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}}}, "output": {"type": "structure", "members": {"accessKeyId": {}, "expiration": {"shape": "S6o"}, "secretAccessKey": {}, "sessionToken": {}}, "sensitive": true}}, "GetEnvironmentProfile": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-profiles/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "environmentBlueprintId", "id", "name"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S6o"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "environmentBlueprintId": {}, "id": {}, "name": {"shape": "S7j"}, "projectId": {}, "updatedAt": {"shape": "S6o"}, "userParameters": {"shape": "S7a"}}}}, "GetFormType": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/form-types/{formTypeIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "formTypeIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "formTypeIdentifier": {"location": "uri", "locationName": "formTypeIdentifier"}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["domainId", "model", "name", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "imports": {"shape": "Scb"}, "model": {"shape": "S7m"}, "name": {"shape": "S37"}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}, "status": {}}}}, "GetGlossary": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/glossaries/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S7r"}, "domainId": {}, "id": {}, "name": {"shape": "S7s"}, "owningProjectId": {}, "status": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetGlossaryTerm": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/glossary-terms/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "glossaryId", "id", "name", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "glossaryId": {}, "id": {}, "longDescription": {"shape": "S7x"}, "name": {"shape": "S13"}, "shortDescription": {"shape": "S14"}, "status": {}, "termRelations": {"shape": "S7z"}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetGroupProfile": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/group-profiles/{groupIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "groupIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "groupIdentifier": {"location": "uri", "locationName": "groupIdentifier"}}}, "output": {"type": "structure", "members": {"domainId": {}, "groupName": {"shape": "S85"}, "id": {}, "status": {}}}}, "GetIamPortalLoginUrl": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/get-portal-login-url", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}}}, "output": {"type": "structure", "required": ["userProfileId"], "members": {"authCodeUrl": {}, "userProfileId": {}}}}, "GetLineageNode": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/lineage/nodes/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "eventTimestamp": {"location": "querystring", "locationName": "timestamp", "type": "timestamp"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "id", "typeName"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {}, "domainId": {}, "downstreamNodes": {"shape": "Sco"}, "eventTimestamp": {"type": "timestamp"}, "formsOutput": {"shape": "S35"}, "id": {}, "name": {}, "sourceIdentifier": {}, "typeName": {}, "typeRevision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}, "upstreamNodes": {"shape": "Sco"}}}}, "GetListing": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/listings/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "listingRevision": {"location": "querystring", "locationName": "listingRevision"}}}, "output": {"type": "structure", "required": ["domainId", "id", "listingRevision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "id": {}, "item": {"type": "structure", "members": {"assetListing": {"type": "structure", "members": {"assetId": {}, "assetRevision": {}, "assetType": {}, "createdAt": {"type": "timestamp"}, "forms": {}, "glossaryTerms": {"shape": "S11"}, "latestTimeSeriesDataPointForms": {"shape": "S38"}, "owningProjectId": {}}}, "dataProductListing": {"type": "structure", "members": {"createdAt": {"type": "timestamp"}, "dataProductId": {}, "dataProductRevision": {}, "forms": {}, "glossaryTerms": {"shape": "S11"}, "items": {"type": "list", "member": {"type": "structure", "members": {"glossaryTerms": {"shape": "S11"}, "listingId": {}, "listingRevision": {}}}}, "owningProjectId": {}}}}, "union": true}, "listingRevision": {}, "name": {}, "status": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetMetadataGenerationRun": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/metadata-generation-runs/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["domainId", "id", "owningProjectId"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "owningProjectId": {}, "status": {}, "target": {"shape": "Sd1"}, "type": {}}}}, "GetProject": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/projects/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "id", "name"], "members": {"createdAt": {"shape": "S6o"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "domainUnitId": {}, "failureReasons": {"shape": "S8e"}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "lastUpdatedAt": {"shape": "S6o"}, "name": {"shape": "S1d"}, "projectStatus": {}}}}, "GetSubscription": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscriptions/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "status", "subscribedListing", "subscribedPrincipal", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "retainPermissions": {"type": "boolean"}, "status": {}, "subscribedListing": {"shape": "St"}, "subscribedPrincipal": {"shape": "S1b"}, "subscriptionRequestId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetSubscriptionGrant": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscription-grants/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "grantedEntity", "id", "status", "subscriptionTargetId", "updatedAt"], "members": {"assets": {"shape": "S8s"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "grantedEntity": {"shape": "S8w"}, "id": {}, "status": {}, "subscriptionId": {"deprecated": true, "deprecatedMessage": "Multiple subscriptions can exist for a single grant"}, "subscriptionTargetId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetSubscriptionRequestDetails": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sl"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sq"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "St"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S1b"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetSubscriptionTarget": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["applicableAssetTypes", "authorizedPrincipals", "createdAt", "created<PERSON>y", "domainId", "environmentId", "id", "manageAccessRole", "name", "projectId", "provider", "subscriptionTargetConfig", "type"], "members": {"applicableAssetTypes": {"shape": "S9a"}, "authorizedPrincipals": {"shape": "S9b"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "environmentId": {}, "id": {}, "manageAccessRole": {}, "name": {"shape": "S9d"}, "projectId": {}, "provider": {}, "subscriptionTargetConfig": {"shape": "S9e"}, "type": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "GetTimeSeriesDataPoint": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/time-series-data-points/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "formName", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "formName": {"location": "querystring", "locationName": "formName"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "members": {"domainId": {}, "entityId": {}, "entityType": {}, "form": {"shape": "Sdk"}, "formName": {}}}}, "GetUserProfile": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/user-profiles/{userIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "userIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "type": {"location": "querystring", "locationName": "type"}, "userIdentifier": {"location": "uri", "locationName": "userIdentifier"}}}, "output": {"type": "structure", "members": {"details": {"shape": "S9k"}, "domainId": {}, "id": {}, "status": {}, "type": {}}}}, "ListAssetFilters": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/assets/{assetIdentifier}/filters", "responseCode": 200}, "input": {"type": "structure", "required": ["assetIdentifier", "domainIdentifier"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["assetId", "domainId", "id", "name"], "members": {"assetId": {}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Su"}, "domainId": {}, "effectiveColumnNames": {"shape": "S3j"}, "effectiveRowFilter": {}, "errorMessage": {}, "id": {}, "name": {"shape": "S41"}, "status": {}}}}, "nextToken": {}}}}, "ListAssetRevisions": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}/revisions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "revision": {}}}}, "nextToken": {}}}}, "ListDataProductRevisions": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-products/{identifier}/revisions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "revision": {}}}}, "nextToken": {}}}}, "ListDataSourceRunActivities": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-source-runs/{identifier}/activities", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["createdAt", "dataAssetStatus", "dataSourceRunId", "database", "projectId", "technicalName", "updatedAt"], "members": {"createdAt": {"shape": "S5r"}, "dataAssetId": {}, "dataAssetStatus": {}, "dataSourceRunId": {}, "database": {"shape": "S5b"}, "errorMessage": {"shape": "S5s"}, "projectId": {}, "technicalDescription": {"shape": "Su"}, "technicalName": {"shape": "S5b"}, "updatedAt": {"shape": "S5r"}}}}, "nextToken": {}}}}, "ListDataSourceRuns": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-sources/{dataSourceIdentifier}/runs", "responseCode": 200}, "input": {"type": "structure", "required": ["dataSourceIdentifier", "domainIdentifier"], "members": {"dataSourceIdentifier": {"location": "uri", "locationName": "dataSourceIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["createdAt", "dataSourceId", "id", "projectId", "status", "type", "updatedAt"], "members": {"createdAt": {"shape": "S5r"}, "dataSourceId": {}, "errorMessage": {"shape": "S5s"}, "id": {}, "projectId": {}, "runStatisticsForAssets": {"shape": "Sbh"}, "startedAt": {"shape": "S5r"}, "status": {}, "stoppedAt": {"shape": "S5r"}, "type": {}, "updatedAt": {"shape": "S5r"}}}}, "nextToken": {}}}}, "ListDataSources": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/data-sources", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "projectIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "querystring", "locationName": "environmentIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"shape": "S5b", "location": "querystring", "locationName": "name"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "projectIdentifier": {"location": "querystring", "locationName": "projectIdentifier"}, "status": {"location": "querystring", "locationName": "status"}, "type": {"location": "querystring", "locationName": "type"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["dataSourceId", "domainId", "environmentId", "name", "status", "type"], "members": {"createdAt": {"shape": "S5r"}, "dataSourceId": {}, "domainId": {}, "enableSetting": {}, "environmentId": {}, "lastRunAssetCount": {"type": "integer"}, "lastRunAt": {"shape": "S5r"}, "lastRunErrorMessage": {"shape": "S5s"}, "lastRunStatus": {}, "name": {"shape": "S5b"}, "schedule": {"shape": "S5d"}, "status": {}, "type": {}, "updatedAt": {"shape": "S5r"}}}}, "nextToken": {}}}}, "ListDomainUnitsForParent": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/domain-units", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "parentDomainUnitIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "parentDomainUnitIdentifier": {"location": "querystring", "locationName": "parentDomainUnitIdentifier"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["id", "name"], "members": {"id": {}, "name": {}}}}, "nextToken": {}}}}, "ListDomains": {"http": {"method": "GET", "requestUri": "/v2/domains", "responseCode": 200}, "input": {"type": "structure", "members": {"maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["arn", "createdAt", "id", "managedAccountId", "name", "status"], "members": {"arn": {}, "createdAt": {"type": "timestamp"}, "description": {"type": "string", "sensitive": true}, "id": {}, "lastUpdatedAt": {"type": "timestamp"}, "managedAccountId": {}, "name": {"type": "string", "sensitive": true}, "portalUrl": {}, "status": {}}}}, "nextToken": {}}}}, "ListEntityOwners": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/owners", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["owners"], "members": {"nextToken": {}, "owners": {"type": "list", "member": {"type": "structure", "members": {"group": {"type": "structure", "members": {"groupId": {}}}, "user": {"type": "structure", "members": {"userId": {}}}}, "union": true}}}}}, "ListEnvironmentActions": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/actions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "parameters"], "members": {"description": {}, "domainId": {}, "environmentId": {}, "id": {}, "name": {}, "parameters": {"shape": "S7e"}}}}, "nextToken": {}}}}, "ListEnvironmentBlueprintConfigurations": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprint-configurations", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["domainId", "environmentBlueprintId"], "members": {"createdAt": {"shape": "S6o"}, "domainId": {}, "enabledRegions": {"shape": "Sbw"}, "environmentBlueprintId": {}, "manageAccessRoleArn": {}, "provisioningConfigurations": {"shape": "Sby"}, "provisioningRoleArn": {}, "regionalParameters": {"shape": "Sc3"}, "updatedAt": {"shape": "S6o"}}}}, "nextToken": {}}}}, "ListEnvironmentBlueprints": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprints", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "managed": {"location": "querystring", "locationName": "managed", "type": "boolean"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"location": "querystring", "locationName": "name"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["id", "name", "provider", "provisioningProperties"], "members": {"createdAt": {"shape": "S6o"}, "description": {"shape": "Su"}, "id": {}, "name": {}, "provider": {}, "provisioningProperties": {"shape": "S77"}, "updatedAt": {"shape": "S6o"}}}}, "nextToken": {}}}}, "ListEnvironmentProfiles": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environment-profiles", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"awsAccountId": {"location": "querystring", "locationName": "awsAccountId"}, "awsAccountRegion": {"location": "querystring", "locationName": "awsAccountRegion"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentBlueprintIdentifier": {"location": "querystring", "locationName": "environmentBlueprintIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"shape": "S7j", "location": "querystring", "locationName": "name"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "projectIdentifier": {"location": "querystring", "locationName": "projectIdentifier"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["created<PERSON>y", "domainId", "environmentBlueprintId", "id", "name"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S6o"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "environmentBlueprintId": {}, "id": {}, "name": {"shape": "S7j"}, "projectId": {}, "updatedAt": {"shape": "S6o"}}}}, "nextToken": {}}}}, "ListEnvironments": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "projectIdentifier"], "members": {"awsAccountId": {"location": "querystring", "locationName": "awsAccountId"}, "awsAccountRegion": {"location": "querystring", "locationName": "awsAccountRegion"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentBlueprintIdentifier": {"location": "querystring", "locationName": "environmentBlueprintIdentifier"}, "environmentProfileIdentifier": {"location": "querystring", "locationName": "environmentProfileIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"location": "querystring", "locationName": "name"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "projectIdentifier": {"location": "querystring", "locationName": "projectIdentifier"}, "provider": {"location": "querystring", "locationName": "provider"}, "status": {"location": "querystring", "locationName": "status"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["created<PERSON>y", "domainId", "name", "projectId", "provider"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S6o"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "environmentProfileId": {}, "id": {}, "name": {"shape": "S74"}, "projectId": {}, "provider": {}, "status": {}, "updatedAt": {"shape": "S6o"}}}}, "nextToken": {}}}}, "ListLineageNodeHistory": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/lineage/nodes/{identifier}/history", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"direction": {"location": "querystring", "locationName": "direction"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "eventTimestampGTE": {"location": "querystring", "locationName": "timestampGTE", "type": "timestamp"}, "eventTimestampLTE": {"location": "querystring", "locationName": "timestampLTE", "type": "timestamp"}, "identifier": {"location": "uri", "locationName": "identifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}}}, "output": {"type": "structure", "members": {"nextToken": {}, "nodes": {"type": "list", "member": {"type": "structure", "required": ["domainId", "id", "typeName"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {}, "domainId": {}, "eventTimestamp": {"type": "timestamp"}, "id": {}, "name": {}, "sourceIdentifier": {}, "typeName": {}, "typeRevision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}}}}, "ListMetadataGenerationRuns": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/metadata-generation-runs", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "status": {"location": "querystring", "locationName": "status"}, "type": {"location": "querystring", "locationName": "type"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["domainId", "id", "owningProjectId"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "owningProjectId": {}, "status": {}, "target": {"shape": "Sd1"}, "type": {}}}}, "nextToken": {}}}}, "ListNotifications": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/notifications", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "type"], "members": {"afterTimestamp": {"location": "querystring", "locationName": "afterTimestamp", "type": "timestamp"}, "beforeTimestamp": {"location": "querystring", "locationName": "beforeTimestamp", "type": "timestamp"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "subjects": {"location": "querystring", "locationName": "subjects", "type": "list", "member": {}}, "taskStatus": {"location": "querystring", "locationName": "taskStatus"}, "type": {"location": "querystring", "locationName": "type"}}}, "output": {"type": "structure", "members": {"nextToken": {}, "notifications": {"type": "list", "member": {"type": "structure", "required": ["actionLink", "creationTimestamp", "domainIdentifier", "identifier", "lastUpdatedTimestamp", "message", "title", "topic", "type"], "members": {"actionLink": {"type": "string", "sensitive": true}, "creationTimestamp": {"type": "timestamp"}, "domainIdentifier": {}, "identifier": {}, "lastUpdatedTimestamp": {"type": "timestamp"}, "message": {"type": "string", "sensitive": true}, "metadata": {"type": "map", "key": {}, "value": {}}, "status": {}, "title": {"type": "string", "sensitive": true}, "topic": {"type": "structure", "required": ["resource", "role", "subject"], "members": {"resource": {"type": "structure", "required": ["id", "type"], "members": {"id": {}, "name": {}, "type": {}}}, "role": {}, "subject": {}}}, "type": {}}}}}}}, "ListPolicyGrants": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/policies/managed/{entityType}/{entityIdentifier}/grants", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "policyType"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "policyType": {"location": "querystring", "locationName": "policyType"}}}, "output": {"type": "structure", "required": ["grantList"], "members": {"grantList": {"type": "list", "member": {"type": "structure", "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "detail": {"shape": "S1p"}, "principal": {"shape": "S24"}}}}, "nextToken": {}}}}, "ListProjectMemberships": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/projects/{projectIdentifier}/memberships", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "projectIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "projectIdentifier": {"location": "uri", "locationName": "projectIdentifier"}, "sortBy": {"location": "querystring", "locationName": "sortBy"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}}}, "output": {"type": "structure", "required": ["members"], "members": {"members": {"type": "list", "member": {"type": "structure", "required": ["designation", "memberDetails"], "members": {"designation": {}, "memberDetails": {"type": "structure", "members": {"group": {"type": "structure", "required": ["groupId"], "members": {"groupId": {}}}, "user": {"type": "structure", "required": ["userId"], "members": {"userId": {}}}}, "union": true}}}}, "nextToken": {}}}}, "ListProjects": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/projects", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "groupIdentifier": {"location": "querystring", "locationName": "groupIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "name": {"shape": "S1d", "location": "querystring", "locationName": "name"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "userIdentifier": {"location": "querystring", "locationName": "userIdentifier"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["created<PERSON>y", "domainId", "id", "name"], "members": {"createdAt": {"shape": "S6o"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "domainUnitId": {}, "failureReasons": {"shape": "S8e"}, "id": {}, "name": {"shape": "S1d"}, "projectStatus": {}, "updatedAt": {"shape": "S6o"}}}}, "nextToken": {}}}}, "ListSubscriptionGrants": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscription-grants", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentId": {"location": "querystring", "locationName": "environmentId"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "owningProjectId": {"location": "querystring", "locationName": "owningProjectId"}, "sortBy": {"location": "querystring", "locationName": "sortBy"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}, "subscribedListingId": {"location": "querystring", "locationName": "subscribedListingId"}, "subscriptionId": {"location": "querystring", "locationName": "subscriptionId"}, "subscriptionTargetId": {"location": "querystring", "locationName": "subscriptionTargetId"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "grantedEntity", "id", "status", "subscriptionTargetId", "updatedAt"], "members": {"assets": {"shape": "S8s"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "grantedEntity": {"shape": "S8w"}, "id": {}, "status": {}, "subscriptionId": {"deprecated": true, "deprecatedMessage": "Multiple subscriptions can exist for a single grant"}, "subscriptionTargetId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "nextToken": {}}}}, "ListSubscriptionRequests": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"approverProjectId": {"location": "querystring", "locationName": "approverProjectId"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "owningProjectId": {"location": "querystring", "locationName": "owningProjectId"}, "sortBy": {"location": "querystring", "locationName": "sortBy"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}, "status": {"location": "querystring", "locationName": "status"}, "subscribedListingId": {"location": "querystring", "locationName": "subscribedListingId"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sl"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sq"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "St"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S1b"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "nextToken": {}}}}, "ListSubscriptionTargets": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "sortBy": {"location": "querystring", "locationName": "sortBy"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["applicableAssetTypes", "authorizedPrincipals", "createdAt", "created<PERSON>y", "domainId", "environmentId", "id", "manageAccessRole", "name", "projectId", "provider", "subscriptionTargetConfig", "type"], "members": {"applicableAssetTypes": {"shape": "S9a"}, "authorizedPrincipals": {"shape": "S9b"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "environmentId": {}, "id": {}, "manageAccessRole": {}, "name": {"shape": "S9d"}, "projectId": {}, "provider": {}, "subscriptionTargetConfig": {"shape": "S9e"}, "type": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "nextToken": {}}}}, "ListSubscriptions": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/subscriptions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"approverProjectId": {"location": "querystring", "locationName": "approverProjectId"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "owningProjectId": {"location": "querystring", "locationName": "owningProjectId"}, "sortBy": {"location": "querystring", "locationName": "sortBy"}, "sortOrder": {"location": "querystring", "locationName": "sortOrder"}, "status": {"location": "querystring", "locationName": "status"}, "subscribedListingId": {"location": "querystring", "locationName": "subscribedListingId"}, "subscriptionRequestIdentifier": {"location": "querystring", "locationName": "subscriptionRequestIdentifier"}}}, "output": {"type": "structure", "required": ["items"], "members": {"items": {"type": "list", "member": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "status", "subscribedListing", "subscribedPrincipal", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "retainPermissions": {"type": "boolean"}, "status": {}, "subscribedListing": {"shape": "St"}, "subscribedPrincipal": {"shape": "S1b"}, "subscriptionRequestId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "nextToken": {}}}}, "ListTagsForResource": {"http": {"method": "GET", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}}}, "output": {"type": "structure", "members": {"tags": {"shape": "S63"}}}}, "ListTimeSeriesDataPoints": {"http": {"method": "GET", "requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/time-series-data-points", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "formName"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "endedAt": {"location": "querystring", "locationName": "endedAt", "type": "timestamp"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "formName": {"location": "querystring", "locationName": "formName"}, "maxResults": {"location": "querystring", "locationName": "maxResults", "type": "integer"}, "nextToken": {"location": "querystring", "locationName": "nextToken"}, "startedAt": {"location": "querystring", "locationName": "startedAt", "type": "timestamp"}}}, "output": {"type": "structure", "members": {"items": {"shape": "S38"}, "nextToken": {}}}}, "PostLineageEvent": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/lineage/events", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "event"], "members": {"clientToken": {"idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "event": {"type": "blob", "sensitive": true}}, "payload": "event"}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "PostTimeSeriesDataPoints": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/time-series-data-points", "responseCode": 201}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "forms"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "forms": {"type": "list", "member": {"type": "structure", "required": ["formName", "timestamp", "typeIdentifier"], "members": {"content": {}, "formName": {}, "timestamp": {"type": "timestamp"}, "typeIdentifier": {}, "typeRevision": {}}}}}}, "output": {"type": "structure", "members": {"domainId": {}, "entityId": {}, "entityType": {}, "forms": {"type": "list", "member": {"shape": "Sdk"}}}}, "idempotent": true}, "PutEnvironmentBlueprintConfiguration": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/environment-blueprint-configurations/{environmentBlueprintIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "enabledRegions", "environmentBlueprintIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "enabledRegions": {"shape": "Sbw"}, "environmentBlueprintIdentifier": {"location": "uri", "locationName": "environmentBlueprintIdentifier"}, "manageAccessRoleArn": {}, "provisioningConfigurations": {"shape": "Sby"}, "provisioningRoleArn": {}, "regionalParameters": {"shape": "Sc3"}}}, "output": {"type": "structure", "required": ["domainId", "environmentBlueprintId"], "members": {"createdAt": {"shape": "S6o"}, "domainId": {}, "enabledRegions": {"shape": "Sbw"}, "environmentBlueprintId": {}, "manageAccessRoleArn": {}, "provisioningConfigurations": {"shape": "Sby"}, "provisioningRoleArn": {}, "regionalParameters": {"shape": "Sc3"}, "updatedAt": {"shape": "S6o"}}}, "idempotent": true}, "RejectPredictions": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/assets/{identifier}/reject-predictions", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "rejectChoices": {"type": "list", "member": {"type": "structure", "required": ["predictionTarget"], "members": {"predictionChoices": {"type": "list", "member": {"type": "integer"}}, "predictionTarget": {}}}}, "rejectRule": {"type": "structure", "members": {"rule": {}, "threshold": {"type": "float"}}}, "revision": {"location": "querystring", "locationName": "revision"}}}, "output": {"type": "structure", "required": ["assetId", "assetRevision", "domainId"], "members": {"assetId": {}, "assetRevision": {}, "domainId": {}}}, "idempotent": true}, "RejectSubscriptionRequest": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests/{identifier}/reject", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"decisionComment": {"shape": "Sl"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sl"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sq"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "St"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S1b"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "RemoveEntityOwner": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/entities/{entityType}/{entityIdentifier}/removeOwner", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "owner"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "owner": {"shape": "S1i"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "RemovePolicyGrant": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/policies/managed/{entityType}/{entityIdentifier}/removeGrant", "responseCode": 204}, "input": {"type": "structure", "required": ["domainIdentifier", "entityIdentifier", "entityType", "policyType", "principal"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "entityIdentifier": {"location": "uri", "locationName": "entityIdentifier"}, "entityType": {"location": "uri", "locationName": "entityType"}, "policyType": {}, "principal": {"shape": "S24"}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "RevokeSubscription": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/subscriptions/{identifier}/revoke", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "retainPermissions": {"type": "boolean"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "status", "subscribedListing", "subscribedPrincipal", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "retainPermissions": {"type": "boolean"}, "status": {}, "subscribedListing": {"shape": "St"}, "subscribedPrincipal": {"shape": "S1b"}, "subscriptionRequestId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "Search": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/search", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "searchScope"], "members": {"additionalAttributes": {"shape": "Si2"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "filters": {"shape": "Si4"}, "maxResults": {"type": "integer"}, "nextToken": {}, "owningProjectIdentifier": {}, "searchIn": {"shape": "Si9"}, "searchScope": {}, "searchText": {}, "sort": {"shape": "Sid"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"assetItem": {"type": "structure", "required": ["domainId", "identifier", "name", "owningProjectId", "typeIdentifier", "typeRevision"], "members": {"additionalAttributes": {"type": "structure", "members": {"formsOutput": {"shape": "S35"}, "latestTimeSeriesDataPointFormsOutput": {"shape": "S38"}, "readOnlyFormsOutput": {"shape": "S35"}}}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "externalIdentifier": {"shape": "S2s"}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "glossaryTerms": {"shape": "S2y"}, "identifier": {}, "name": {"shape": "S30"}, "owningProjectId": {}, "typeIdentifier": {}, "typeRevision": {}}}, "dataProductItem": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S4d"}, "domainId": {}, "firstRevisionCreatedAt": {"type": "timestamp"}, "firstRevisionCreatedBy": {}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "name": {"shape": "S4j"}, "owningProjectId": {}}}, "glossaryItem": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S7r"}, "domainId": {}, "id": {}, "name": {"shape": "S7s"}, "owningProjectId": {}, "status": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "glossaryTermItem": {"type": "structure", "required": ["domainId", "glossaryId", "id", "name", "status"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "glossaryId": {}, "id": {}, "longDescription": {"shape": "S7x"}, "name": {"shape": "S13"}, "shortDescription": {"shape": "S14"}, "status": {}, "termRelations": {"shape": "S7z"}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "union": true}}, "nextToken": {}, "totalMatchCount": {"type": "integer"}}}}, "SearchGroupProfiles": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/search-group-profiles", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "groupType"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "groupType": {}, "maxResults": {"type": "integer"}, "nextToken": {}, "searchText": {"type": "string", "sensitive": true}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"domainId": {}, "groupName": {"shape": "S85"}, "id": {}, "status": {}}}}, "nextToken": {}}}}, "SearchListings": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/listings/search", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier"], "members": {"additionalAttributes": {"shape": "Si2"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "filters": {"shape": "Si4"}, "maxResults": {"type": "integer"}, "nextToken": {}, "searchIn": {"shape": "Si9"}, "searchText": {}, "sort": {"shape": "Sid"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"assetListing": {"type": "structure", "members": {"additionalAttributes": {"type": "structure", "members": {"forms": {}, "latestTimeSeriesDataPointForms": {"shape": "S38"}}}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Su"}, "entityId": {}, "entityRevision": {}, "entityType": {}, "glossaryTerms": {"shape": "S11"}, "listingCreatedBy": {}, "listingId": {}, "listingRevision": {}, "listingUpdatedBy": {}, "name": {"shape": "S30"}, "owningProjectId": {}}}, "dataProductListing": {"type": "structure", "members": {"additionalAttributes": {"type": "structure", "members": {"forms": {}}}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Su"}, "entityId": {}, "entityRevision": {}, "glossaryTerms": {"shape": "S11"}, "items": {"type": "list", "member": {"type": "structure", "members": {"glossaryTerms": {"shape": "S11"}, "listingId": {}, "listingRevision": {}}}}, "listingCreatedBy": {}, "listingId": {}, "listingRevision": {}, "listingUpdatedBy": {}, "name": {"shape": "S4j"}, "owningProjectId": {}}}}, "union": true}}, "nextToken": {}, "totalMatchCount": {"type": "integer"}}}}, "SearchTypes": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/types-search", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "managed", "searchScope"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "filters": {"shape": "Si4"}, "managed": {"type": "boolean"}, "maxResults": {"type": "integer"}, "nextToken": {}, "searchIn": {"shape": "Si9"}, "searchScope": {}, "searchText": {}, "sort": {"shape": "Sid"}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"assetTypeItem": {"type": "structure", "required": ["domainId", "formsOutput", "name", "owningProjectId", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "formsOutput": {"shape": "S4a"}, "name": {}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "formTypeItem": {"type": "structure", "required": ["domainId", "name", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "imports": {"shape": "Scb"}, "model": {"shape": "S7m"}, "name": {"shape": "S37"}, "originDomainId": {}, "originProjectId": {}, "owningProjectId": {}, "revision": {}, "status": {}}}, "lineageNodeTypeItem": {"type": "structure", "required": ["domainId", "formsOutput", "revision"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {}, "domainId": {}, "formsOutput": {"shape": "S4a"}, "name": {}, "revision": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}}, "union": true}}, "nextToken": {}, "totalMatchCount": {"type": "integer"}}}}, "SearchUserProfiles": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/search-user-profiles", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "userType"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "maxResults": {"type": "integer"}, "nextToken": {}, "searchText": {"type": "string", "sensitive": true}, "userType": {}}}, "output": {"type": "structure", "members": {"items": {"type": "list", "member": {"type": "structure", "members": {"details": {"shape": "S9k"}, "domainId": {}, "id": {}, "status": {}, "type": {}}}}, "nextToken": {}}}}, "StartDataSourceRun": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/data-sources/{dataSourceIdentifier}/runs", "responseCode": 200}, "input": {"type": "structure", "required": ["dataSourceIdentifier", "domainIdentifier"], "members": {"clientToken": {"idempotencyToken": true}, "dataSourceIdentifier": {"location": "uri", "locationName": "dataSourceIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}}}, "output": {"type": "structure", "required": ["createdAt", "dataSourceId", "domainId", "id", "projectId", "status", "type", "updatedAt"], "members": {"createdAt": {"shape": "S5r"}, "dataSourceConfigurationSnapshot": {}, "dataSourceId": {}, "domainId": {}, "errorMessage": {"shape": "S5s"}, "id": {}, "projectId": {}, "runStatisticsForAssets": {"shape": "Sbh"}, "startedAt": {"shape": "S5r"}, "status": {}, "stoppedAt": {"shape": "S5r"}, "type": {}, "updatedAt": {"shape": "S5r"}}}, "idempotent": true}, "StartMetadataGenerationRun": {"http": {"requestUri": "/v2/domains/{domainIdentifier}/metadata-generation-runs", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "owningProjectIdentifier", "target", "type"], "members": {"clientToken": {"idempotencyToken": true}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "owningProjectIdentifier": {}, "target": {"shape": "Sd1"}, "type": {}}}, "output": {"type": "structure", "required": ["domainId", "id"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "id": {}, "owningProjectId": {}, "status": {}, "type": {}}}, "idempotent": true}, "TagResource": {"http": {"requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "tags": {"shape": "S63"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"http": {"method": "DELETE", "requestUri": "/tags/{resourceArn}", "responseCode": 200}, "input": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {"location": "uri", "locationName": "resourceArn"}, "tagKeys": {"location": "querystring", "locationName": "tagKeys", "type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}, "idempotent": true}, "UpdateAssetFilter": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/assets/{assetIdentifier}/filters/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["assetIdentifier", "domainIdentifier", "identifier"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "configuration": {"shape": "S3h"}, "description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {}}}, "output": {"type": "structure", "required": ["assetId", "configuration", "domainId", "id", "name"], "members": {"assetId": {}, "configuration": {"shape": "S3h"}, "createdAt": {"type": "timestamp"}, "description": {"shape": "Su"}, "domainId": {}, "effectiveColumnNames": {"shape": "S3j"}, "effectiveRowFilter": {}, "errorMessage": {}, "id": {}, "name": {"shape": "S41"}, "status": {}}}, "idempotent": true}, "UpdateDataSource": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/data-sources/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"assetFormsInput": {"shape": "S2t"}, "configuration": {"shape": "S4q"}, "description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "enableSetting": {}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S5b"}, "publishOnImport": {"type": "boolean"}, "recommendation": {"shape": "S5c"}, "retainPermissionsOnRevokeFailure": {"type": "boolean"}, "schedule": {"shape": "S5d"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "projectId"], "members": {"assetFormsOutput": {"shape": "S35"}, "configuration": {"shape": "S5i"}, "createdAt": {"shape": "S5r"}, "description": {"shape": "Su"}, "domainId": {}, "enableSetting": {}, "environmentId": {}, "errorMessage": {"shape": "S5s"}, "id": {}, "lastRunAt": {"shape": "S5r"}, "lastRunErrorMessage": {"shape": "S5s"}, "lastRunStatus": {}, "name": {"shape": "S5b"}, "projectId": {}, "publishOnImport": {"type": "boolean"}, "recommendation": {"shape": "S5c"}, "retainPermissionsOnRevokeFailure": {"type": "boolean"}, "schedule": {"shape": "S5d"}, "selfGrantStatus": {"shape": "Sa2"}, "status": {}, "type": {}, "updatedAt": {"shape": "S5r"}}}, "idempotent": true}, "UpdateDomain": {"http": {"method": "PUT", "requestUri": "/v2/domains/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["identifier"], "members": {"clientToken": {"idempotencyToken": true, "location": "querystring", "locationName": "clientToken"}, "description": {}, "domainExecutionRole": {}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {}, "singleSignOn": {"shape": "S60"}}}, "output": {"type": "structure", "required": ["id"], "members": {"description": {}, "domainExecutionRole": {}, "id": {}, "lastUpdatedAt": {"type": "timestamp"}, "name": {}, "rootDomainUnitId": {}, "singleSignOn": {"shape": "S60"}}}, "idempotent": true}, "UpdateDomainUnit": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/domain-units/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"description": {"shape": "S69"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S6a"}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owners"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "description": {"shape": "S69"}, "domainId": {}, "id": {}, "lastUpdatedAt": {"type": "timestamp"}, "lastUpdatedBy": {}, "name": {"shape": "S6a"}, "owners": {"shape": "S6d"}, "parentDomainUnitId": {}}}, "idempotent": true}, "UpdateEnvironment": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/environments/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"description": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "glossaryTerms": {"shape": "S2y"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "name", "projectId", "provider"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S6o"}, "createdBy": {}, "deploymentProperties": {"shape": "S6p"}, "description": {"shape": "Su"}, "domainId": {}, "environmentActions": {"shape": "S6s"}, "environmentBlueprintId": {}, "environmentProfileId": {}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "lastDeployment": {"shape": "S6y"}, "name": {"shape": "S74"}, "projectId": {}, "provider": {}, "provisionedResources": {"shape": "S75"}, "provisioningProperties": {"shape": "S77"}, "status": {}, "updatedAt": {"shape": "S6o"}, "userParameters": {"shape": "S7a"}}}}, "UpdateEnvironmentAction": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/actions/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"description": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {}, "parameters": {"shape": "S7e"}}}, "output": {"type": "structure", "required": ["domainId", "environmentId", "id", "name", "parameters"], "members": {"description": {}, "domainId": {}, "environmentId": {}, "id": {}, "name": {}, "parameters": {"shape": "S7e"}}}}, "UpdateEnvironmentProfile": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/environment-profiles/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "description": {}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S7j"}, "userParameters": {"shape": "S6j"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "environmentBlueprintId", "id", "name"], "members": {"awsAccountId": {}, "awsAccountRegion": {}, "createdAt": {"shape": "S6o"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "environmentBlueprintId": {}, "id": {}, "name": {"shape": "S7j"}, "projectId": {}, "updatedAt": {"shape": "S6o"}, "userParameters": {"shape": "S7a"}}}}, "UpdateGlossary": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/glossaries/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"clientToken": {"idempotencyToken": true}, "description": {"shape": "S7r"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S7s"}, "status": {}}}, "output": {"type": "structure", "required": ["domainId", "id", "name", "owningProjectId"], "members": {"description": {"shape": "S7r"}, "domainId": {}, "id": {}, "name": {"shape": "S7s"}, "owningProjectId": {}, "status": {}}}, "idempotent": true}, "UpdateGlossaryTerm": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/glossary-terms/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "glossaryIdentifier": {}, "identifier": {"location": "uri", "locationName": "identifier"}, "longDescription": {"shape": "S7x"}, "name": {"shape": "S13"}, "shortDescription": {"shape": "S14"}, "status": {}, "termRelations": {"shape": "S7z"}}}, "output": {"type": "structure", "required": ["domainId", "glossaryId", "id", "name", "status"], "members": {"domainId": {}, "glossaryId": {}, "id": {}, "longDescription": {"shape": "S7x"}, "name": {"shape": "S13"}, "shortDescription": {"shape": "S14"}, "status": {}, "termRelations": {"shape": "S7z"}}}, "idempotent": true}, "UpdateGroupProfile": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/group-profiles/{groupIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "groupIdentifier", "status"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "groupIdentifier": {"location": "uri", "locationName": "groupIdentifier"}, "status": {}}}, "output": {"type": "structure", "members": {"domainId": {}, "groupName": {"shape": "S85"}, "id": {}, "status": {}}}}, "UpdateProject": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/projects/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier"], "members": {"description": {"shape": "Su"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "glossaryTerms": {"shape": "S2y"}, "identifier": {"location": "uri", "locationName": "identifier"}, "name": {"shape": "S1d"}}}, "output": {"type": "structure", "required": ["created<PERSON>y", "domainId", "id", "name"], "members": {"createdAt": {"shape": "S6o"}, "createdBy": {}, "description": {"shape": "Su"}, "domainId": {}, "domainUnitId": {}, "failureReasons": {"shape": "S8e"}, "glossaryTerms": {"shape": "S2y"}, "id": {}, "lastUpdatedAt": {"shape": "S6o"}, "name": {"shape": "S1d"}, "projectStatus": {}}}, "idempotent": true}, "UpdateSubscriptionGrantStatus": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/subscription-grants/{identifier}/status/{assetIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["assetIdentifier", "domainIdentifier", "identifier", "status"], "members": {"assetIdentifier": {"location": "uri", "locationName": "assetIdentifier"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "failureCause": {"shape": "S8u"}, "identifier": {"location": "uri", "locationName": "identifier"}, "status": {}, "targetName": {}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "grantedEntity", "id", "status", "subscriptionTargetId", "updatedAt"], "members": {"assets": {"shape": "S8s"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "grantedEntity": {"shape": "S8w"}, "id": {}, "status": {}, "subscriptionId": {"deprecated": true, "deprecatedMessage": "Multiple subscriptions can exist for a single grant"}, "subscriptionTargetId": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "UpdateSubscriptionRequest": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/subscription-requests/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "identifier", "requestReason"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "requestReason": {"shape": "Sq"}}}, "output": {"type": "structure", "required": ["createdAt", "created<PERSON>y", "domainId", "id", "requestReason", "status", "subscribedListings", "subscribedPrincipals", "updatedAt"], "members": {"createdAt": {"type": "timestamp"}, "createdBy": {}, "decisionComment": {"shape": "Sl"}, "domainId": {}, "id": {}, "requestReason": {"shape": "Sq"}, "reviewerId": {}, "status": {}, "subscribedListings": {"type": "list", "member": {"shape": "St"}}, "subscribedPrincipals": {"type": "list", "member": {"shape": "S1b"}}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "UpdateSubscriptionTarget": {"http": {"method": "PATCH", "requestUri": "/v2/domains/{domainIdentifier}/environments/{environmentIdentifier}/subscription-targets/{identifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "environmentIdentifier", "identifier"], "members": {"applicableAssetTypes": {"shape": "S9a"}, "authorizedPrincipals": {"shape": "S9b"}, "domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "environmentIdentifier": {"location": "uri", "locationName": "environmentIdentifier"}, "identifier": {"location": "uri", "locationName": "identifier"}, "manageAccessRole": {}, "name": {"shape": "S9d"}, "provider": {}, "subscriptionTargetConfig": {"shape": "S9e"}}}, "output": {"type": "structure", "required": ["applicableAssetTypes", "authorizedPrincipals", "createdAt", "created<PERSON>y", "domainId", "environmentId", "id", "manageAccessRole", "name", "projectId", "provider", "subscriptionTargetConfig", "type"], "members": {"applicableAssetTypes": {"shape": "S9a"}, "authorizedPrincipals": {"shape": "S9b"}, "createdAt": {"type": "timestamp"}, "createdBy": {}, "domainId": {}, "environmentId": {}, "id": {}, "manageAccessRole": {}, "name": {"shape": "S9d"}, "projectId": {}, "provider": {}, "subscriptionTargetConfig": {"shape": "S9e"}, "type": {}, "updatedAt": {"type": "timestamp"}, "updatedBy": {}}}, "idempotent": true}, "UpdateUserProfile": {"http": {"method": "PUT", "requestUri": "/v2/domains/{domainIdentifier}/user-profiles/{userIdentifier}", "responseCode": 200}, "input": {"type": "structure", "required": ["domainIdentifier", "status", "userIdentifier"], "members": {"domainIdentifier": {"location": "uri", "locationName": "domainIdentifier"}, "status": {}, "type": {}, "userIdentifier": {"location": "uri", "locationName": "userIdentifier"}}}, "output": {"type": "structure", "members": {"details": {"shape": "S9k"}, "domainId": {}, "id": {}, "status": {}, "type": {}}}}}, "shapes": {"Sj": {"type": "list", "member": {}}, "Sl": {"type": "string", "sensitive": true}, "Sq": {"type": "string", "sensitive": true}, "St": {"type": "structure", "required": ["description", "id", "item", "name", "ownerProjectId"], "members": {"description": {"shape": "Su"}, "id": {}, "item": {"type": "structure", "members": {"assetListing": {"type": "structure", "members": {"assetScope": {"shape": "Sy"}, "entityId": {}, "entityRevision": {}, "entityType": {}, "forms": {}, "glossaryTerms": {"shape": "S11"}}}, "productListing": {"type": "structure", "members": {"assetListings": {"type": "list", "member": {"type": "structure", "members": {"entityId": {}, "entityRevision": {}, "entityType": {}}}}, "description": {}, "entityId": {}, "entityRevision": {}, "glossaryTerms": {"shape": "S11"}, "name": {}}}}, "union": true}, "name": {}, "ownerProjectId": {}, "ownerProjectName": {}, "revision": {}}}, "Su": {"type": "string", "sensitive": true}, "Sy": {"type": "structure", "required": ["assetId", "filterIds", "status"], "members": {"assetId": {}, "errorMessage": {}, "filterIds": {"shape": "Sj"}, "status": {}}}, "S11": {"type": "list", "member": {"type": "structure", "members": {"name": {"shape": "S13"}, "shortDescription": {"shape": "S14"}}}}, "S13": {"type": "string", "sensitive": true}, "S14": {"type": "string", "sensitive": true}, "S1b": {"type": "structure", "members": {"project": {"type": "structure", "members": {"id": {}, "name": {"shape": "S1d"}}}}, "union": true}, "S1d": {"type": "string", "sensitive": true}, "S1i": {"type": "structure", "members": {"group": {"type": "structure", "required": ["groupIdentifier"], "members": {"groupIdentifier": {}}}, "user": {"type": "structure", "required": ["userIdentifier"], "members": {"userIdentifier": {}}}}, "union": true}, "S1p": {"type": "structure", "members": {"addToProjectMemberPool": {"type": "structure", "members": {"includeChildDomainUnits": {"type": "boolean"}}}, "createAssetType": {"type": "structure", "members": {"includeChildDomainUnits": {"type": "boolean"}}}, "createDomainUnit": {"type": "structure", "members": {"includeChildDomainUnits": {"type": "boolean"}}}, "createEnvironment": {"shape": "S1u"}, "createEnvironmentProfile": {"type": "structure", "members": {"domainUnitId": {}}}, "createFormType": {"type": "structure", "members": {"includeChildDomainUnits": {"type": "boolean"}}}, "createGlossary": {"type": "structure", "members": {"includeChildDomainUnits": {"type": "boolean"}}}, "createProject": {"type": "structure", "members": {"includeChildDomainUnits": {"type": "boolean"}}}, "delegateCreateEnvironmentProfile": {"shape": "S1u"}, "overrideDomainUnitOwners": {"type": "structure", "members": {"includeChildDomainUnits": {"type": "boolean"}}}, "overrideProjectOwners": {"type": "structure", "members": {"includeChildDomainUnits": {"type": "boolean"}}}}, "union": true}, "S1u": {"type": "structure", "members": {}}, "S24": {"type": "structure", "members": {"domainUnit": {"type": "structure", "required": ["domainUnitDesignation"], "members": {"domainUnitDesignation": {}, "domainUnitGrantFilter": {"type": "structure", "members": {"allDomainUnitsGrantFilter": {"type": "structure", "members": {}}}, "union": true}, "domainUnitIdentifier": {}}}, "group": {"type": "structure", "members": {"groupIdentifier": {}}, "union": true}, "project": {"type": "structure", "required": ["projectDesignation"], "members": {"projectDesignation": {}, "projectGrantFilter": {"type": "structure", "members": {"domainUnitFilter": {"type": "structure", "required": ["domainUnit"], "members": {"domainUnit": {}, "includeChildDomainUnits": {"type": "boolean"}}}}, "union": true}, "projectIdentifier": {}}}, "user": {"type": "structure", "members": {"allUsersGrantFilter": {"type": "structure", "members": {}}, "userIdentifier": {}}, "union": true}}, "union": true}, "S2s": {"type": "string", "sensitive": true}, "S2t": {"type": "list", "member": {"type": "structure", "required": ["formName"], "members": {"content": {}, "formName": {}, "typeIdentifier": {}, "typeRevision": {}}, "sensitive": true}, "sensitive": true}, "S2y": {"type": "list", "member": {}}, "S30": {"type": "string", "sensitive": true}, "S31": {"type": "structure", "members": {"businessNameGeneration": {"type": "structure", "members": {"enabled": {"type": "boolean"}}}}}, "S35": {"type": "list", "member": {"type": "structure", "required": ["formName"], "members": {"content": {}, "formName": {}, "typeName": {"shape": "S37"}, "typeRevision": {}}}}, "S37": {"type": "string", "sensitive": true}, "S38": {"type": "list", "member": {"type": "structure", "required": ["formName", "timestamp", "typeIdentifier"], "members": {"contentSummary": {}, "formName": {}, "id": {}, "timestamp": {"type": "timestamp"}, "typeIdentifier": {}, "typeRevision": {}}}}, "S3e": {"type": "structure", "required": ["listingId", "listingStatus"], "members": {"listingId": {}, "listingStatus": {}}}, "S3h": {"type": "structure", "members": {"columnConfiguration": {"type": "structure", "members": {"includedColumnNames": {"shape": "S3j"}}}, "rowConfiguration": {"type": "structure", "required": ["rowFilter"], "members": {"rowFilter": {"shape": "S3l"}, "sensitive": {"type": "boolean"}}}}, "union": true}, "S3j": {"type": "list", "member": {}}, "S3l": {"type": "structure", "members": {"and": {"shape": "S3m"}, "expression": {"type": "structure", "members": {"equalTo": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "greaterThan": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "greaterThanOrEqualTo": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "in": {"type": "structure", "required": ["columnName", "values"], "members": {"columnName": {}, "values": {"shape": "S3s"}}}, "isNotNull": {"type": "structure", "required": ["columnName"], "members": {"columnName": {}}}, "isNull": {"type": "structure", "required": ["columnName"], "members": {"columnName": {}}}, "lessThan": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "lessThanOrEqualTo": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "like": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "notEqualTo": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}, "notIn": {"type": "structure", "required": ["columnName", "values"], "members": {"columnName": {}, "values": {"shape": "S3s"}}}, "notLike": {"type": "structure", "required": ["columnName", "value"], "members": {"columnName": {}, "value": {}}}}, "union": true}, "or": {"shape": "S3m"}}, "union": true}, "S3m": {"type": "list", "member": {"shape": "S3l"}}, "S3s": {"type": "list", "member": {}}, "S41": {"type": "string", "sensitive": true}, "S4a": {"type": "map", "key": {}, "value": {"type": "structure", "required": ["typeName", "typeRevision"], "members": {"required": {"type": "boolean"}, "typeName": {"shape": "S37"}, "typeRevision": {}}}}, "S4d": {"type": "string", "sensitive": true}, "S4e": {"type": "list", "member": {"type": "structure", "required": ["identifier", "itemType"], "members": {"glossaryTerms": {"type": "list", "member": {}}, "identifier": {}, "itemType": {}, "revision": {}}}}, "S4j": {"type": "string", "sensitive": true}, "S4q": {"type": "structure", "members": {"glueRunConfiguration": {"type": "structure", "required": ["relationalFilterConfigurations"], "members": {"autoImportDataQualityResult": {"type": "boolean"}, "dataAccessRole": {}, "relationalFilterConfigurations": {"shape": "S4t"}}}, "redshiftRunConfiguration": {"type": "structure", "required": ["redshiftCredentialConfiguration", "redshiftStorage", "relationalFilterConfigurations"], "members": {"dataAccessRole": {}, "redshiftCredentialConfiguration": {"shape": "S53"}, "redshiftStorage": {"shape": "S55"}, "relationalFilterConfigurations": {"shape": "S4t"}}}}, "union": true}, "S4t": {"type": "list", "member": {"type": "structure", "required": ["databaseName"], "members": {"databaseName": {}, "filterExpressions": {"type": "list", "member": {"type": "structure", "required": ["expression", "type"], "members": {"expression": {}, "type": {}}}}, "schemaName": {}}}}, "S53": {"type": "structure", "required": ["secretManagerArn"], "members": {"secretManagerArn": {}}}, "S55": {"type": "structure", "members": {"redshiftClusterSource": {"type": "structure", "required": ["clusterName"], "members": {"clusterName": {}}}, "redshiftServerlessSource": {"type": "structure", "required": ["workgroupName"], "members": {"workgroupName": {}}}}, "union": true}, "S5b": {"type": "string", "sensitive": true}, "S5c": {"type": "structure", "members": {"enableBusinessNameGeneration": {"type": "boolean"}}}, "S5d": {"type": "structure", "members": {"schedule": {}, "timezone": {}}, "sensitive": true}, "S5i": {"type": "structure", "members": {"glueRunConfiguration": {"type": "structure", "required": ["relationalFilterConfigurations"], "members": {"accountId": {}, "autoImportDataQualityResult": {"type": "boolean"}, "dataAccessRole": {}, "region": {}, "relationalFilterConfigurations": {"shape": "S4t"}}}, "redshiftRunConfiguration": {"type": "structure", "required": ["redshiftCredentialConfiguration", "redshiftStorage", "relationalFilterConfigurations"], "members": {"accountId": {}, "dataAccessRole": {}, "redshiftCredentialConfiguration": {"shape": "S53"}, "redshiftStorage": {"shape": "S55"}, "region": {}, "relationalFilterConfigurations": {"shape": "S4t"}}}}, "union": true}, "S5r": {"type": "timestamp", "timestampFormat": "iso8601"}, "S5s": {"type": "structure", "required": ["errorType"], "members": {"errorDetail": {}, "errorType": {}}}, "S60": {"type": "structure", "members": {"type": {}, "userAssignment": {}}}, "S63": {"type": "map", "key": {}, "value": {}}, "S69": {"type": "string", "sensitive": true}, "S6a": {"type": "string", "sensitive": true}, "S6d": {"type": "list", "member": {"type": "structure", "members": {"group": {"type": "structure", "members": {"groupId": {}}}, "user": {"type": "structure", "members": {"userId": {}}}}, "union": true}}, "S6j": {"type": "list", "member": {"type": "structure", "members": {"name": {}, "value": {}}}}, "S6o": {"type": "timestamp", "timestampFormat": "iso8601"}, "S6p": {"type": "structure", "members": {"endTimeoutMinutes": {"type": "integer"}, "startTimeoutMinutes": {"type": "integer"}}}, "S6s": {"type": "list", "member": {"type": "structure", "required": ["parameters", "type"], "members": {"auth": {}, "parameters": {"type": "list", "member": {"type": "structure", "members": {"key": {}, "value": {}}}}, "type": {}}}}, "S6y": {"type": "structure", "members": {"deploymentId": {}, "deploymentStatus": {}, "deploymentType": {}, "failureReason": {"type": "structure", "required": ["message"], "members": {"code": {}, "message": {}}}, "isDeploymentComplete": {"type": "boolean"}, "messages": {"type": "list", "member": {}}}}, "S74": {"type": "string", "sensitive": true}, "S75": {"type": "list", "member": {"type": "structure", "required": ["type", "value"], "members": {"name": {}, "provider": {}, "type": {}, "value": {}}}}, "S77": {"type": "structure", "members": {"cloudFormation": {"type": "structure", "required": ["templateUrl"], "members": {"templateUrl": {}}}}, "union": true}, "S7a": {"type": "list", "member": {"type": "structure", "required": ["fieldType", "keyName"], "members": {"defaultValue": {}, "description": {"shape": "Su"}, "fieldType": {}, "isEditable": {"type": "boolean"}, "isOptional": {"type": "boolean"}, "keyName": {}}}}, "S7e": {"type": "structure", "members": {"awsConsoleLink": {"type": "structure", "members": {"uri": {}}}}, "union": true}, "S7j": {"type": "string", "sensitive": true}, "S7m": {"type": "structure", "members": {"smithy": {}}, "sensitive": true, "union": true}, "S7r": {"type": "string", "sensitive": true}, "S7s": {"type": "string", "sensitive": true}, "S7x": {"type": "string", "sensitive": true}, "S7z": {"type": "structure", "members": {"classifies": {"type": "list", "member": {}}, "isA": {"type": "list", "member": {}}}}, "S85": {"type": "string", "sensitive": true}, "S8e": {"type": "list", "member": {"type": "structure", "members": {"code": {}, "message": {}}}}, "S8j": {"type": "structure", "members": {"groupIdentifier": {}, "userIdentifier": {}}, "union": true}, "S8s": {"type": "list", "member": {"type": "structure", "required": ["assetId", "assetRevision", "status"], "members": {"assetId": {}, "assetRevision": {}, "assetScope": {"shape": "Sy"}, "failureCause": {"shape": "S8u"}, "failureTimestamp": {"type": "timestamp"}, "grantedTimestamp": {"type": "timestamp"}, "status": {}, "targetName": {}}}}, "S8u": {"type": "structure", "members": {"message": {}}}, "S8w": {"type": "structure", "members": {"listing": {"type": "structure", "required": ["id", "revision"], "members": {"id": {}, "revision": {}}}}, "union": true}, "S9a": {"type": "list", "member": {}}, "S9b": {"type": "list", "member": {}}, "S9d": {"type": "string", "sensitive": true}, "S9e": {"type": "list", "member": {"type": "structure", "required": ["content", "formName"], "members": {"content": {}, "formName": {}}}}, "S9k": {"type": "structure", "members": {"iam": {"type": "structure", "members": {"arn": {}}}, "sso": {"type": "structure", "members": {"firstName": {"type": "string", "sensitive": true}, "lastName": {"type": "string", "sensitive": true}, "username": {"type": "string", "sensitive": true}}}}, "union": true}, "Sa2": {"type": "structure", "members": {"glueSelfGrantStatus": {"type": "structure", "required": ["selfGrantStatusDetails"], "members": {"selfGrantStatusDetails": {"shape": "Sa4"}}}, "redshiftSelfGrantStatus": {"type": "structure", "required": ["selfGrantStatusDetails"], "members": {"selfGrantStatusDetails": {"shape": "Sa4"}}}}, "union": true}, "Sa4": {"type": "list", "member": {"type": "structure", "required": ["databaseName", "status"], "members": {"databaseName": {}, "failureCause": {}, "schemaName": {}, "status": {}}}}, "Sbh": {"type": "structure", "members": {"added": {"type": "integer"}, "failed": {"type": "integer"}, "skipped": {"type": "integer"}, "unchanged": {"type": "integer"}, "updated": {"type": "integer"}}}, "Sbw": {"type": "list", "member": {}}, "Sby": {"type": "list", "member": {"type": "structure", "members": {"lakeFormationConfiguration": {"type": "structure", "members": {"locationRegistrationExcludeS3Locations": {"type": "list", "member": {}}, "locationRegistrationRole": {}}}}, "union": true}}, "Sc3": {"type": "map", "key": {}, "value": {"type": "map", "key": {}, "value": {}}}, "Scb": {"type": "list", "member": {"type": "structure", "required": ["name", "revision"], "members": {"name": {"shape": "S37"}, "revision": {}}}}, "Sco": {"type": "list", "member": {"type": "structure", "members": {"eventTimestamp": {"type": "timestamp"}, "id": {}}}}, "Sd1": {"type": "structure", "required": ["identifier", "type"], "members": {"identifier": {}, "revision": {}, "type": {}}}, "Sdk": {"type": "structure", "required": ["formName", "timestamp", "typeIdentifier"], "members": {"content": {}, "formName": {}, "id": {}, "timestamp": {"type": "timestamp"}, "typeIdentifier": {}, "typeRevision": {}}}, "Si2": {"type": "list", "member": {}}, "Si4": {"type": "structure", "members": {"and": {"shape": "Si5"}, "filter": {"type": "structure", "required": ["attribute", "value"], "members": {"attribute": {}, "value": {}}}, "or": {"shape": "Si5"}}, "union": true}, "Si5": {"type": "list", "member": {"shape": "Si4"}}, "Si9": {"type": "list", "member": {"type": "structure", "required": ["attribute"], "members": {"attribute": {}}}}, "Sid": {"type": "structure", "required": ["attribute"], "members": {"attribute": {}, "order": {}}}}}