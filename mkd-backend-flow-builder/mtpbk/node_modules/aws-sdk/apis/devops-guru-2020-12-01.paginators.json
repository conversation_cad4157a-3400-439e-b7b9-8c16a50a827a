{"pagination": {"DescribeOrganizationResourceCollectionHealth": {"input_token": "NextToken", "output_token": "NextToken", "result_key": ["CloudFormation", "Account", "Service", "Tags"]}, "DescribeResourceCollectionHealth": {"input_token": "NextToken", "output_token": "NextToken", "result_key": ["CloudFormation", "Service", "Tags"]}, "GetCostEstimation": {"input_token": "NextToken", "non_aggregate_keys": ["Status", "TotalCost", "TimeRange", "ResourceCollection"], "output_token": "NextToken", "result_key": ["Costs"]}, "GetResourceCollection": {"input_token": "NextToken", "non_aggregate_keys": ["ResourceCollection"], "output_token": "NextToken", "result_key": ["ResourceCollection.CloudFormation.StackNames", "ResourceCollection.Tags"]}, "ListAnomaliesForInsight": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": ["ReactiveAnomalies", "ProactiveAnomalies"]}, "ListAnomalousLogGroups": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": ["InsightId", "AnomalousLogGroups"]}, "ListEvents": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": "Events"}, "ListInsights": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": ["ProactiveInsights", "ReactiveInsights"]}, "ListMonitoredResources": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": ["MonitoredResourceIdentifiers"]}, "ListNotificationChannels": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "Channels"}, "ListOrganizationInsights": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": ["ProactiveInsights", "ReactiveInsights"]}, "ListRecommendations": {"input_token": "NextToken", "output_token": "NextToken", "result_key": "Recommendations"}, "SearchInsights": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": ["ProactiveInsights", "ReactiveInsights"]}, "SearchOrganizationInsights": {"input_token": "NextToken", "limit_key": "MaxResults", "output_token": "NextToken", "result_key": ["ProactiveInsights", "ReactiveInsights"]}}}