{"version": "2.0", "metadata": {"apiVersion": "2014-11-13", "endpointPrefix": "ecs", "jsonVersion": "1.1", "protocol": "json", "protocols": ["json"], "serviceAbbreviation": "Amazon ECS", "serviceFullName": "Amazon EC2 Container Service", "serviceId": "ECS", "signatureVersion": "v4", "targetPrefix": "AmazonEC2ContainerServiceV20141113", "uid": "ecs-2014-11-13", "auth": ["aws.auth#sigv4"]}, "operations": {"CreateCapacityProvider": {"input": {"type": "structure", "required": ["name", "autoScalingGroupProvider"], "members": {"name": {}, "autoScalingGroupProvider": {"shape": "S3"}, "tags": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {"capacityProvider": {"shape": "Sg"}}}}, "CreateCluster": {"input": {"type": "structure", "members": {"clusterName": {}, "tags": {"shape": "Sb"}, "settings": {"shape": "Sk"}, "configuration": {"shape": "Sn"}, "capacityProviders": {"shape": "St"}, "defaultCapacityProviderStrategy": {"shape": "Su"}, "serviceConnectDefaults": {"shape": "Sy"}}}, "output": {"type": "structure", "members": {"cluster": {"shape": "S10"}}}}, "CreateService": {"input": {"type": "structure", "required": ["serviceName"], "members": {"cluster": {}, "serviceName": {}, "taskDefinition": {}, "loadBalancers": {"shape": "S19"}, "serviceRegistries": {"shape": "S1c"}, "desiredCount": {"type": "integer"}, "clientToken": {}, "launchType": {}, "capacityProviderStrategy": {"shape": "Su"}, "platformVersion": {}, "role": {}, "deploymentConfiguration": {"shape": "S1f"}, "placementConstraints": {"shape": "S1i"}, "placementStrategy": {"shape": "S1l"}, "networkConfiguration": {"shape": "S1o"}, "healthCheckGracePeriodSeconds": {"type": "integer"}, "schedulingStrategy": {}, "deploymentController": {"shape": "S1s"}, "tags": {"shape": "Sb"}, "enableECSManagedTags": {"type": "boolean"}, "propagateTags": {}, "enableExecuteCommand": {"type": "boolean"}, "serviceConnectConfiguration": {"shape": "S1v"}, "volumeConfigurations": {"shape": "S2a"}}}, "output": {"type": "structure", "members": {"service": {"shape": "S2o"}}}}, "CreateTaskSet": {"input": {"type": "structure", "required": ["service", "cluster", "taskDefinition"], "members": {"service": {}, "cluster": {}, "externalId": {}, "taskDefinition": {}, "networkConfiguration": {"shape": "S1o"}, "loadBalancers": {"shape": "S19"}, "serviceRegistries": {"shape": "S1c"}, "launchType": {}, "capacityProviderStrategy": {"shape": "Su"}, "platformVersion": {}, "scale": {"shape": "S2s"}, "clientToken": {}, "tags": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {"taskSet": {"shape": "S2q"}}}}, "DeleteAccountSetting": {"input": {"type": "structure", "required": ["name"], "members": {"name": {}, "principalArn": {}}}, "output": {"type": "structure", "members": {"setting": {"shape": "S39"}}}}, "DeleteAttributes": {"input": {"type": "structure", "required": ["attributes"], "members": {"cluster": {}, "attributes": {"shape": "S3c"}}}, "output": {"type": "structure", "members": {"attributes": {"shape": "S3c"}}}}, "DeleteCapacityProvider": {"input": {"type": "structure", "required": ["capacityProvider"], "members": {"capacityProvider": {}}}, "output": {"type": "structure", "members": {"capacityProvider": {"shape": "Sg"}}}}, "DeleteCluster": {"input": {"type": "structure", "required": ["cluster"], "members": {"cluster": {}}}, "output": {"type": "structure", "members": {"cluster": {"shape": "S10"}}}}, "DeleteService": {"input": {"type": "structure", "required": ["service"], "members": {"cluster": {}, "service": {}, "force": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"service": {"shape": "S2o"}}}}, "DeleteTaskDefinitions": {"input": {"type": "structure", "required": ["taskDefinitions"], "members": {"taskDefinitions": {"shape": "St"}}}, "output": {"type": "structure", "members": {"taskDefinitions": {"type": "list", "member": {"shape": "S3p"}}, "failures": {"shape": "S5s"}}}}, "DeleteTaskSet": {"input": {"type": "structure", "required": ["cluster", "service", "taskSet"], "members": {"cluster": {}, "service": {}, "taskSet": {}, "force": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"taskSet": {"shape": "S2q"}}}}, "DeregisterContainerInstance": {"input": {"type": "structure", "required": ["containerInstance"], "members": {"cluster": {}, "containerInstance": {}, "force": {"type": "boolean"}}}, "output": {"type": "structure", "members": {"containerInstance": {"shape": "S5y"}}}}, "DeregisterTaskDefinition": {"input": {"type": "structure", "required": ["taskDefinition"], "members": {"taskDefinition": {}}}, "output": {"type": "structure", "members": {"taskDefinition": {"shape": "S3p"}}}}, "DescribeCapacityProviders": {"input": {"type": "structure", "members": {"capacityProviders": {"shape": "St"}, "include": {"type": "list", "member": {}}, "maxResults": {"type": "integer"}, "nextToken": {}}}, "output": {"type": "structure", "members": {"capacityProviders": {"type": "list", "member": {"shape": "Sg"}}, "failures": {"shape": "S5s"}, "nextToken": {}}}}, "DescribeClusters": {"input": {"type": "structure", "members": {"clusters": {"shape": "St"}, "include": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"clusters": {"type": "list", "member": {"shape": "S10"}}, "failures": {"shape": "S5s"}}}}, "DescribeContainerInstances": {"input": {"type": "structure", "required": ["containerInstances"], "members": {"cluster": {}, "containerInstances": {"shape": "St"}, "include": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"containerInstances": {"shape": "S6p"}, "failures": {"shape": "S5s"}}}}, "DescribeServices": {"input": {"type": "structure", "required": ["services"], "members": {"cluster": {}, "services": {"shape": "St"}, "include": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"services": {"type": "list", "member": {"shape": "S2o"}}, "failures": {"shape": "S5s"}}}}, "DescribeTaskDefinition": {"input": {"type": "structure", "required": ["taskDefinition"], "members": {"taskDefinition": {}, "include": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"taskDefinition": {"shape": "S3p"}, "tags": {"shape": "Sb"}}}}, "DescribeTaskSets": {"input": {"type": "structure", "required": ["cluster", "service"], "members": {"cluster": {}, "service": {}, "taskSets": {"shape": "St"}, "include": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"taskSets": {"shape": "S2p"}, "failures": {"shape": "S5s"}}}}, "DescribeTasks": {"input": {"type": "structure", "required": ["tasks"], "members": {"cluster": {}, "tasks": {"shape": "St"}, "include": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {"tasks": {"shape": "S77"}, "failures": {"shape": "S5s"}}}}, "DiscoverPollEndpoint": {"input": {"type": "structure", "members": {"containerInstance": {}, "cluster": {}}}, "output": {"type": "structure", "members": {"endpoint": {}, "telemetryEndpoint": {}, "serviceConnectEndpoint": {}}}}, "ExecuteCommand": {"input": {"type": "structure", "required": ["command", "interactive", "task"], "members": {"cluster": {}, "container": {}, "command": {}, "interactive": {"type": "boolean"}, "task": {}}}, "output": {"type": "structure", "members": {"clusterArn": {}, "containerArn": {}, "containerName": {}, "interactive": {"type": "boolean"}, "session": {"type": "structure", "members": {"sessionId": {}, "streamUrl": {}, "tokenValue": {"type": "string", "sensitive": true}}}, "taskArn": {}}}}, "GetTaskProtection": {"input": {"type": "structure", "required": ["cluster"], "members": {"cluster": {}, "tasks": {"shape": "St"}}}, "output": {"type": "structure", "members": {"protectedTasks": {"shape": "S80"}, "failures": {"shape": "S5s"}}}}, "ListAccountSettings": {"input": {"type": "structure", "members": {"name": {}, "value": {}, "principalArn": {}, "effectiveSettings": {"type": "boolean"}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"settings": {"type": "list", "member": {"shape": "S39"}}, "nextToken": {}}}}, "ListAttributes": {"input": {"type": "structure", "required": ["targetType"], "members": {"cluster": {}, "targetType": {}, "attributeName": {}, "attributeValue": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"attributes": {"shape": "S3c"}, "nextToken": {}}}}, "ListClusters": {"input": {"type": "structure", "members": {"nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"clusterArns": {"shape": "St"}, "nextToken": {}}}}, "ListContainerInstances": {"input": {"type": "structure", "members": {"cluster": {}, "filter": {}, "nextToken": {}, "maxResults": {"type": "integer"}, "status": {}}}, "output": {"type": "structure", "members": {"containerInstanceArns": {"shape": "St"}, "nextToken": {}}}}, "ListServices": {"input": {"type": "structure", "members": {"cluster": {}, "nextToken": {}, "maxResults": {"type": "integer"}, "launchType": {}, "schedulingStrategy": {}}}, "output": {"type": "structure", "members": {"serviceArns": {"shape": "St"}, "nextToken": {}}}}, "ListServicesByNamespace": {"input": {"type": "structure", "required": ["namespace"], "members": {"namespace": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"serviceArns": {"shape": "St"}, "nextToken": {}}}}, "ListTagsForResource": {"input": {"type": "structure", "required": ["resourceArn"], "members": {"resourceArn": {}}}, "output": {"type": "structure", "members": {"tags": {"shape": "Sb"}}}}, "ListTaskDefinitionFamilies": {"input": {"type": "structure", "members": {"familyPrefix": {}, "status": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"families": {"shape": "St"}, "nextToken": {}}}}, "ListTaskDefinitions": {"input": {"type": "structure", "members": {"familyPrefix": {}, "status": {}, "sort": {}, "nextToken": {}, "maxResults": {"type": "integer"}}}, "output": {"type": "structure", "members": {"taskDefinitionArns": {"shape": "St"}, "nextToken": {}}}}, "ListTasks": {"input": {"type": "structure", "members": {"cluster": {}, "containerInstance": {}, "family": {}, "nextToken": {}, "maxResults": {"type": "integer"}, "startedBy": {}, "serviceName": {}, "desiredStatus": {}, "launchType": {}}}, "output": {"type": "structure", "members": {"taskArns": {"shape": "St"}, "nextToken": {}}}}, "PutAccountSetting": {"input": {"type": "structure", "required": ["name", "value"], "members": {"name": {}, "value": {}, "principalArn": {}}}, "output": {"type": "structure", "members": {"setting": {"shape": "S39"}}}}, "PutAccountSettingDefault": {"input": {"type": "structure", "required": ["name", "value"], "members": {"name": {}, "value": {}}}, "output": {"type": "structure", "members": {"setting": {"shape": "S39"}}}}, "PutAttributes": {"input": {"type": "structure", "required": ["attributes"], "members": {"cluster": {}, "attributes": {"shape": "S3c"}}}, "output": {"type": "structure", "members": {"attributes": {"shape": "S3c"}}}}, "PutClusterCapacityProviders": {"input": {"type": "structure", "required": ["cluster", "capacityProviders", "defaultCapacityProviderStrategy"], "members": {"cluster": {}, "capacityProviders": {"shape": "St"}, "defaultCapacityProviderStrategy": {"shape": "Su"}}}, "output": {"type": "structure", "members": {"cluster": {"shape": "S10"}}}}, "RegisterContainerInstance": {"input": {"type": "structure", "members": {"cluster": {}, "instanceIdentityDocument": {}, "instanceIdentityDocumentSignature": {}, "totalResources": {"shape": "S61"}, "versionInfo": {"shape": "S60"}, "containerInstanceArn": {}, "attributes": {"shape": "S3c"}, "platformDevices": {"type": "list", "member": {"type": "structure", "required": ["id", "type"], "members": {"id": {}, "type": {}}}}, "tags": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {"containerInstance": {"shape": "S5y"}}}}, "RegisterTaskDefinition": {"input": {"type": "structure", "required": ["family", "containerDefinitions"], "members": {"family": {}, "taskRoleArn": {}, "executionRoleArn": {}, "networkMode": {}, "containerDefinitions": {"shape": "S3q"}, "volumes": {"shape": "S4y"}, "placementConstraints": {"shape": "S5c"}, "requiresCompatibilities": {"shape": "S5f"}, "cpu": {}, "memory": {}, "tags": {"shape": "Sb"}, "pidMode": {}, "ipcMode": {}, "proxyConfiguration": {"shape": "S5o"}, "inferenceAccelerators": {"shape": "S5k"}, "ephemeralStorage": {"shape": "S5r"}, "runtimePlatform": {"shape": "S5h"}}}, "output": {"type": "structure", "members": {"taskDefinition": {"shape": "S3p"}, "tags": {"shape": "Sb"}}}}, "RunTask": {"input": {"type": "structure", "required": ["taskDefinition"], "members": {"capacityProviderStrategy": {"shape": "Su"}, "cluster": {}, "count": {"type": "integer"}, "enableECSManagedTags": {"type": "boolean"}, "enableExecuteCommand": {"type": "boolean"}, "group": {}, "launchType": {}, "networkConfiguration": {"shape": "S1o"}, "overrides": {"shape": "S7l"}, "placementConstraints": {"shape": "S1i"}, "placementStrategy": {"shape": "S1l"}, "platformVersion": {}, "propagateTags": {}, "referenceId": {}, "startedBy": {}, "tags": {"shape": "Sb"}, "taskDefinition": {}, "clientToken": {"idempotencyToken": true}, "volumeConfigurations": {"shape": "S97"}}}, "output": {"type": "structure", "members": {"tasks": {"shape": "S77"}, "failures": {"shape": "S5s"}}}}, "StartTask": {"input": {"type": "structure", "required": ["containerInstances", "taskDefinition"], "members": {"cluster": {}, "containerInstances": {"shape": "St"}, "enableECSManagedTags": {"type": "boolean"}, "enableExecuteCommand": {"type": "boolean"}, "group": {}, "networkConfiguration": {"shape": "S1o"}, "overrides": {"shape": "S7l"}, "propagateTags": {}, "referenceId": {}, "startedBy": {}, "tags": {"shape": "Sb"}, "taskDefinition": {}, "volumeConfigurations": {"shape": "S97"}}}, "output": {"type": "structure", "members": {"tasks": {"shape": "S77"}, "failures": {"shape": "S5s"}}}}, "StopTask": {"input": {"type": "structure", "required": ["task"], "members": {"cluster": {}, "task": {}, "reason": {}}}, "output": {"type": "structure", "members": {"task": {"shape": "S78"}}}}, "SubmitAttachmentStateChanges": {"input": {"type": "structure", "required": ["attachments"], "members": {"cluster": {}, "attachments": {"shape": "S9h"}}}, "output": {"type": "structure", "members": {"acknowledgment": {}}}}, "SubmitContainerStateChange": {"input": {"type": "structure", "members": {"cluster": {}, "task": {}, "containerName": {}, "runtimeId": {}, "status": {}, "exitCode": {"type": "integer"}, "reason": {}, "networkBindings": {"shape": "S7c"}}}, "output": {"type": "structure", "members": {"acknowledgment": {}}}}, "SubmitTaskStateChange": {"input": {"type": "structure", "members": {"cluster": {}, "task": {}, "status": {}, "reason": {}, "containers": {"type": "list", "member": {"type": "structure", "members": {"containerName": {}, "imageDigest": {}, "runtimeId": {}, "exitCode": {"type": "integer"}, "networkBindings": {"shape": "S7c"}, "reason": {}, "status": {}}}}, "attachments": {"shape": "S9h"}, "managedAgents": {"type": "list", "member": {"type": "structure", "required": ["containerName", "managedAgentName", "status"], "members": {"containerName": {}, "managedAgentName": {}, "status": {}, "reason": {}}}}, "pullStartedAt": {"type": "timestamp"}, "pullStoppedAt": {"type": "timestamp"}, "executionStoppedAt": {"type": "timestamp"}}}, "output": {"type": "structure", "members": {"acknowledgment": {}}}}, "TagResource": {"input": {"type": "structure", "required": ["resourceArn", "tags"], "members": {"resourceArn": {}, "tags": {"shape": "Sb"}}}, "output": {"type": "structure", "members": {}}}, "UntagResource": {"input": {"type": "structure", "required": ["resourceArn", "tagKeys"], "members": {"resourceArn": {}, "tagKeys": {"type": "list", "member": {}}}}, "output": {"type": "structure", "members": {}}}, "UpdateCapacityProvider": {"input": {"type": "structure", "required": ["name", "autoScalingGroupProvider"], "members": {"name": {}, "autoScalingGroupProvider": {"type": "structure", "members": {"managedScaling": {"shape": "S4"}, "managedTerminationProtection": {}, "managedDraining": {}}}}}, "output": {"type": "structure", "members": {"capacityProvider": {"shape": "Sg"}}}}, "UpdateCluster": {"input": {"type": "structure", "required": ["cluster"], "members": {"cluster": {}, "settings": {"shape": "Sk"}, "configuration": {"shape": "Sn"}, "serviceConnectDefaults": {"shape": "Sy"}}}, "output": {"type": "structure", "members": {"cluster": {"shape": "S10"}}}}, "UpdateClusterSettings": {"input": {"type": "structure", "required": ["cluster", "settings"], "members": {"cluster": {}, "settings": {"shape": "Sk"}}}, "output": {"type": "structure", "members": {"cluster": {"shape": "S10"}}}}, "UpdateContainerAgent": {"input": {"type": "structure", "required": ["containerInstance"], "members": {"cluster": {}, "containerInstance": {}}}, "output": {"type": "structure", "members": {"containerInstance": {"shape": "S5y"}}}}, "UpdateContainerInstancesState": {"input": {"type": "structure", "required": ["containerInstances", "status"], "members": {"cluster": {}, "containerInstances": {"shape": "St"}, "status": {}}}, "output": {"type": "structure", "members": {"containerInstances": {"shape": "S6p"}, "failures": {"shape": "S5s"}}}}, "UpdateService": {"input": {"type": "structure", "required": ["service"], "members": {"cluster": {}, "service": {}, "desiredCount": {"type": "integer"}, "taskDefinition": {}, "capacityProviderStrategy": {"shape": "Su"}, "deploymentConfiguration": {"shape": "S1f"}, "networkConfiguration": {"shape": "S1o"}, "placementConstraints": {"shape": "S1i"}, "placementStrategy": {"shape": "S1l"}, "platformVersion": {}, "forceNewDeployment": {"type": "boolean"}, "healthCheckGracePeriodSeconds": {"type": "integer"}, "enableExecuteCommand": {"type": "boolean"}, "enableECSManagedTags": {"type": "boolean"}, "loadBalancers": {"shape": "S19"}, "propagateTags": {}, "serviceRegistries": {"shape": "S1c"}, "serviceConnectConfiguration": {"shape": "S1v"}, "volumeConfigurations": {"shape": "S2a"}}}, "output": {"type": "structure", "members": {"service": {"shape": "S2o"}}}}, "UpdateServicePrimaryTaskSet": {"input": {"type": "structure", "required": ["cluster", "service", "primaryTaskSet"], "members": {"cluster": {}, "service": {}, "primaryTaskSet": {}}}, "output": {"type": "structure", "members": {"taskSet": {"shape": "S2q"}}}}, "UpdateTaskProtection": {"input": {"type": "structure", "required": ["cluster", "tasks", "protectionEnabled"], "members": {"cluster": {}, "tasks": {"shape": "St"}, "protectionEnabled": {"type": "boolean"}, "expiresInMinutes": {"type": "integer"}}}, "output": {"type": "structure", "members": {"protectedTasks": {"shape": "S80"}, "failures": {"shape": "S5s"}}}}, "UpdateTaskSet": {"input": {"type": "structure", "required": ["cluster", "service", "taskSet", "scale"], "members": {"cluster": {}, "service": {}, "taskSet": {}, "scale": {"shape": "S2s"}}}, "output": {"type": "structure", "members": {"taskSet": {"shape": "S2q"}}}}}, "shapes": {"S3": {"type": "structure", "required": ["autoScalingGroupArn"], "members": {"autoScalingGroupArn": {}, "managedScaling": {"shape": "S4"}, "managedTerminationProtection": {}, "managedDraining": {}}}, "S4": {"type": "structure", "members": {"status": {}, "targetCapacity": {"type": "integer"}, "minimumScalingStepSize": {"type": "integer"}, "maximumScalingStepSize": {"type": "integer"}, "instanceWarmupPeriod": {"type": "integer"}}}, "Sb": {"type": "list", "member": {"type": "structure", "members": {"key": {}, "value": {}}}}, "Sg": {"type": "structure", "members": {"capacityProviderArn": {}, "name": {}, "status": {}, "autoScalingGroupProvider": {"shape": "S3"}, "updateStatus": {}, "updateStatusReason": {}, "tags": {"shape": "Sb"}}}, "Sk": {"type": "list", "member": {"type": "structure", "members": {"name": {}, "value": {}}}}, "Sn": {"type": "structure", "members": {"executeCommandConfiguration": {"type": "structure", "members": {"kmsKeyId": {}, "logging": {}, "logConfiguration": {"type": "structure", "members": {"cloudWatchLogGroupName": {}, "cloudWatchEncryptionEnabled": {"type": "boolean"}, "s3BucketName": {}, "s3EncryptionEnabled": {"type": "boolean"}, "s3KeyPrefix": {}}}}}, "managedStorageConfiguration": {"type": "structure", "members": {"kmsKeyId": {}, "fargateEphemeralStorageKmsKeyId": {}}}}}, "St": {"type": "list", "member": {}}, "Su": {"type": "list", "member": {"type": "structure", "required": ["capacityProvider"], "members": {"capacityProvider": {}, "weight": {"type": "integer"}, "base": {"type": "integer"}}}}, "Sy": {"type": "structure", "required": ["namespace"], "members": {"namespace": {}}}, "S10": {"type": "structure", "members": {"clusterArn": {}, "clusterName": {}, "configuration": {"shape": "Sn"}, "status": {}, "registeredContainerInstancesCount": {"type": "integer"}, "runningTasksCount": {"type": "integer"}, "pendingTasksCount": {"type": "integer"}, "activeServicesCount": {"type": "integer"}, "statistics": {"type": "list", "member": {"shape": "S13"}}, "tags": {"shape": "Sb"}, "settings": {"shape": "Sk"}, "capacityProviders": {"shape": "St"}, "defaultCapacityProviderStrategy": {"shape": "Su"}, "attachments": {"shape": "S14"}, "attachmentsStatus": {}, "serviceConnectDefaults": {"type": "structure", "members": {"namespace": {}}}}}, "S13": {"type": "structure", "members": {"name": {}, "value": {}}}, "S14": {"type": "list", "member": {"type": "structure", "members": {"id": {}, "type": {}, "status": {}, "details": {"type": "list", "member": {"shape": "S13"}}}}}, "S19": {"type": "list", "member": {"type": "structure", "members": {"targetGroupArn": {}, "loadBalancerName": {}, "containerName": {}, "containerPort": {"type": "integer"}}}}, "S1c": {"type": "list", "member": {"type": "structure", "members": {"registryArn": {}, "port": {"type": "integer"}, "containerName": {}, "containerPort": {"type": "integer"}}}}, "S1f": {"type": "structure", "members": {"deploymentCircuitBreaker": {"type": "structure", "required": ["enable", "rollback"], "members": {"enable": {"type": "boolean"}, "rollback": {"type": "boolean"}}}, "maximumPercent": {"type": "integer"}, "minimumHealthyPercent": {"type": "integer"}, "alarms": {"type": "structure", "required": ["alarmNames", "enable", "rollback"], "members": {"alarmNames": {"shape": "St"}, "enable": {"type": "boolean"}, "rollback": {"type": "boolean"}}}}}, "S1i": {"type": "list", "member": {"type": "structure", "members": {"type": {}, "expression": {}}}}, "S1l": {"type": "list", "member": {"type": "structure", "members": {"type": {}, "field": {}}}}, "S1o": {"type": "structure", "members": {"awsvpcConfiguration": {"type": "structure", "required": ["subnets"], "members": {"subnets": {"shape": "St"}, "securityGroups": {"shape": "St"}, "assignPublicIp": {}}}}}, "S1s": {"type": "structure", "required": ["type"], "members": {"type": {}}}, "S1v": {"type": "structure", "required": ["enabled"], "members": {"enabled": {"type": "boolean"}, "namespace": {}, "services": {"type": "list", "member": {"type": "structure", "required": ["portName"], "members": {"portName": {}, "discoveryName": {}, "clientAliases": {"type": "list", "member": {"type": "structure", "required": ["port"], "members": {"port": {"type": "integer"}, "dnsName": {}}}}, "ingressPortOverride": {"type": "integer"}, "timeout": {"type": "structure", "members": {"idleTimeoutSeconds": {"type": "integer"}, "perRequestTimeoutSeconds": {"type": "integer"}}}, "tls": {"type": "structure", "required": ["issuerCertificateAuthority"], "members": {"issuerCertificateAuthority": {"type": "structure", "members": {"awsPcaAuthorityArn": {}}}, "kmsKey": {}, "roleArn": {}}}}}}, "logConfiguration": {"shape": "S25"}}}, "S25": {"type": "structure", "required": ["logDriver"], "members": {"logDriver": {}, "options": {"type": "map", "key": {}, "value": {}}, "secretOptions": {"shape": "S28"}}}, "S28": {"type": "list", "member": {"type": "structure", "required": ["name", "valueFrom"], "members": {"name": {}, "valueFrom": {}}}}, "S2a": {"type": "list", "member": {"type": "structure", "required": ["name"], "members": {"name": {}, "managedEBSVolume": {"type": "structure", "required": ["roleArn"], "members": {"encrypted": {"type": "boolean"}, "kmsKeyId": {}, "volumeType": {}, "sizeInGiB": {"type": "integer"}, "snapshotId": {}, "iops": {"type": "integer"}, "throughput": {"type": "integer"}, "tagSpecifications": {"shape": "S2i"}, "roleArn": {}, "filesystemType": {}}}}}}, "S2i": {"type": "list", "member": {"type": "structure", "required": ["resourceType"], "members": {"resourceType": {}, "tags": {"shape": "Sb"}, "propagateTags": {}}}}, "S2o": {"type": "structure", "members": {"serviceArn": {}, "serviceName": {}, "clusterArn": {}, "loadBalancers": {"shape": "S19"}, "serviceRegistries": {"shape": "S1c"}, "status": {}, "desiredCount": {"type": "integer"}, "runningCount": {"type": "integer"}, "pendingCount": {"type": "integer"}, "launchType": {}, "capacityProviderStrategy": {"shape": "Su"}, "platformVersion": {}, "platformFamily": {}, "taskDefinition": {}, "deploymentConfiguration": {"shape": "S1f"}, "taskSets": {"shape": "S2p"}, "deployments": {"type": "list", "member": {"type": "structure", "members": {"id": {}, "status": {}, "taskDefinition": {}, "desiredCount": {"type": "integer"}, "pendingCount": {"type": "integer"}, "runningCount": {"type": "integer"}, "failedTasks": {"type": "integer"}, "createdAt": {"type": "timestamp"}, "updatedAt": {"type": "timestamp"}, "capacityProviderStrategy": {"shape": "Su"}, "launchType": {}, "platformVersion": {}, "platformFamily": {}, "networkConfiguration": {"shape": "S1o"}, "rolloutState": {}, "rolloutStateReason": {}, "serviceConnectConfiguration": {"shape": "S1v"}, "serviceConnectResources": {"type": "list", "member": {"type": "structure", "members": {"discoveryName": {}, "discoveryArn": {}}}}, "volumeConfigurations": {"shape": "S2a"}, "fargateEphemeralStorage": {"shape": "S2w"}}}}, "roleArn": {}, "events": {"type": "list", "member": {"type": "structure", "members": {"id": {}, "createdAt": {"type": "timestamp"}, "message": {}}}}, "createdAt": {"type": "timestamp"}, "placementConstraints": {"shape": "S1i"}, "placementStrategy": {"shape": "S1l"}, "networkConfiguration": {"shape": "S1o"}, "healthCheckGracePeriodSeconds": {"type": "integer"}, "schedulingStrategy": {}, "deploymentController": {"shape": "S1s"}, "tags": {"shape": "Sb"}, "createdBy": {}, "enableECSManagedTags": {"type": "boolean"}, "propagateTags": {}, "enableExecuteCommand": {"type": "boolean"}}}, "S2p": {"type": "list", "member": {"shape": "S2q"}}, "S2q": {"type": "structure", "members": {"id": {}, "taskSetArn": {}, "serviceArn": {}, "clusterArn": {}, "startedBy": {}, "externalId": {}, "status": {}, "taskDefinition": {}, "computedDesiredCount": {"type": "integer"}, "pendingCount": {"type": "integer"}, "runningCount": {"type": "integer"}, "createdAt": {"type": "timestamp"}, "updatedAt": {"type": "timestamp"}, "launchType": {}, "capacityProviderStrategy": {"shape": "Su"}, "platformVersion": {}, "platformFamily": {}, "networkConfiguration": {"shape": "S1o"}, "loadBalancers": {"shape": "S19"}, "serviceRegistries": {"shape": "S1c"}, "scale": {"shape": "S2s"}, "stabilityStatus": {}, "stabilityStatusAt": {"type": "timestamp"}, "tags": {"shape": "Sb"}, "fargateEphemeralStorage": {"shape": "S2w"}}}, "S2s": {"type": "structure", "members": {"value": {"type": "double"}, "unit": {}}}, "S2w": {"type": "structure", "members": {"kmsKeyId": {}}}, "S39": {"type": "structure", "members": {"name": {}, "value": {}, "principalArn": {}, "type": {}}}, "S3c": {"type": "list", "member": {"shape": "S3d"}}, "S3d": {"type": "structure", "required": ["name"], "members": {"name": {}, "value": {}, "targetType": {}, "targetId": {}}}, "S3p": {"type": "structure", "members": {"taskDefinitionArn": {}, "containerDefinitions": {"shape": "S3q"}, "family": {}, "taskRoleArn": {}, "executionRoleArn": {}, "networkMode": {}, "revision": {"type": "integer"}, "volumes": {"shape": "S4y"}, "status": {}, "requiresAttributes": {"type": "list", "member": {"shape": "S3d"}}, "placementConstraints": {"shape": "S5c"}, "compatibilities": {"shape": "S5f"}, "runtimePlatform": {"shape": "S5h"}, "requiresCompatibilities": {"shape": "S5f"}, "cpu": {}, "memory": {}, "inferenceAccelerators": {"shape": "S5k"}, "pidMode": {}, "ipcMode": {}, "proxyConfiguration": {"shape": "S5o"}, "registeredAt": {"type": "timestamp"}, "deregisteredAt": {"type": "timestamp"}, "registeredBy": {}, "ephemeralStorage": {"shape": "S5r"}}}, "S3q": {"type": "list", "member": {"type": "structure", "members": {"name": {}, "image": {}, "repositoryCredentials": {"type": "structure", "required": ["credentialsParameter"], "members": {"credentialsParameter": {}}}, "cpu": {"type": "integer"}, "memory": {"type": "integer"}, "memoryReservation": {"type": "integer"}, "links": {"shape": "St"}, "portMappings": {"type": "list", "member": {"type": "structure", "members": {"containerPort": {"type": "integer"}, "hostPort": {"type": "integer"}, "protocol": {}, "name": {}, "appProtocol": {}, "containerPortRange": {}}}}, "essential": {"type": "boolean"}, "restartPolicy": {"type": "structure", "required": ["enabled"], "members": {"enabled": {"type": "boolean"}, "ignoredExitCodes": {"type": "list", "member": {"type": "integer"}}, "restartAttemptPeriod": {"type": "integer"}}}, "entryPoint": {"shape": "St"}, "command": {"shape": "St"}, "environment": {"shape": "S3z"}, "environmentFiles": {"shape": "S40"}, "mountPoints": {"type": "list", "member": {"type": "structure", "members": {"sourceVolume": {}, "containerPath": {}, "readOnly": {"type": "boolean"}}}}, "volumesFrom": {"type": "list", "member": {"type": "structure", "members": {"sourceContainer": {}, "readOnly": {"type": "boolean"}}}}, "linuxParameters": {"type": "structure", "members": {"capabilities": {"type": "structure", "members": {"add": {"shape": "St"}, "drop": {"shape": "St"}}}, "devices": {"type": "list", "member": {"type": "structure", "required": ["hostPath"], "members": {"hostPath": {}, "containerPath": {}, "permissions": {"type": "list", "member": {}}}}}, "initProcessEnabled": {"type": "boolean"}, "sharedMemorySize": {"type": "integer"}, "tmpfs": {"type": "list", "member": {"type": "structure", "required": ["containerPath", "size"], "members": {"containerPath": {}, "size": {"type": "integer"}, "mountOptions": {"shape": "St"}}}}, "maxSwap": {"type": "integer"}, "swappiness": {"type": "integer"}}}, "secrets": {"shape": "S28"}, "dependsOn": {"type": "list", "member": {"type": "structure", "required": ["containerName", "condition"], "members": {"containerName": {}, "condition": {}}}}, "startTimeout": {"type": "integer"}, "stopTimeout": {"type": "integer"}, "hostname": {}, "user": {}, "workingDirectory": {}, "disableNetworking": {"type": "boolean"}, "privileged": {"type": "boolean"}, "readonlyRootFilesystem": {"type": "boolean"}, "dnsServers": {"shape": "St"}, "dnsSearchDomains": {"shape": "St"}, "extraHosts": {"type": "list", "member": {"type": "structure", "required": ["hostname", "ip<PERSON><PERSON><PERSON>"], "members": {"hostname": {}, "ipAddress": {}}}}, "dockerSecurityOptions": {"shape": "St"}, "interactive": {"type": "boolean"}, "pseudoTerminal": {"type": "boolean"}, "dockerLabels": {"type": "map", "key": {}, "value": {}}, "ulimits": {"type": "list", "member": {"type": "structure", "required": ["name", "softLimit", "hardLimit"], "members": {"name": {}, "softLimit": {"type": "integer"}, "hardLimit": {"type": "integer"}}}}, "logConfiguration": {"shape": "S25"}, "healthCheck": {"type": "structure", "required": ["command"], "members": {"command": {"shape": "St"}, "interval": {"type": "integer"}, "timeout": {"type": "integer"}, "retries": {"type": "integer"}, "startPeriod": {"type": "integer"}}}, "systemControls": {"type": "list", "member": {"type": "structure", "members": {"namespace": {}, "value": {}}}}, "resourceRequirements": {"shape": "S4r"}, "firelensConfiguration": {"type": "structure", "required": ["type"], "members": {"type": {}, "options": {"type": "map", "key": {}, "value": {}}}}, "credentialSpecs": {"shape": "St"}}}}, "S3z": {"type": "list", "member": {"shape": "S13"}}, "S40": {"type": "list", "member": {"type": "structure", "required": ["value", "type"], "members": {"value": {}, "type": {}}}}, "S4r": {"type": "list", "member": {"type": "structure", "required": ["value", "type"], "members": {"value": {}, "type": {}}}}, "S4y": {"type": "list", "member": {"type": "structure", "members": {"name": {}, "host": {"type": "structure", "members": {"sourcePath": {}}}, "dockerVolumeConfiguration": {"type": "structure", "members": {"scope": {}, "autoprovision": {"type": "boolean"}, "driver": {}, "driverOpts": {"shape": "S53"}, "labels": {"shape": "S53"}}}, "efsVolumeConfiguration": {"type": "structure", "required": ["fileSystemId"], "members": {"fileSystemId": {}, "rootDirectory": {}, "transitEncryption": {}, "transitEncryptionPort": {"type": "integer"}, "authorizationConfig": {"type": "structure", "members": {"accessPointId": {}, "iam": {}}}}}, "fsxWindowsFileServerVolumeConfiguration": {"type": "structure", "required": ["fileSystemId", "rootDirectory", "authorizationConfig"], "members": {"fileSystemId": {}, "rootDirectory": {}, "authorizationConfig": {"type": "structure", "required": ["credentialsParameter", "domain"], "members": {"credentialsParameter": {}, "domain": {}}}}}, "configuredAtLaunch": {"type": "boolean"}}}}, "S53": {"type": "map", "key": {}, "value": {}}, "S5c": {"type": "list", "member": {"type": "structure", "members": {"type": {}, "expression": {}}}}, "S5f": {"type": "list", "member": {}}, "S5h": {"type": "structure", "members": {"cpuArchitecture": {}, "operatingSystemFamily": {}}}, "S5k": {"type": "list", "member": {"type": "structure", "required": ["deviceName", "deviceType"], "members": {"deviceName": {}, "deviceType": {}}}}, "S5o": {"type": "structure", "required": ["containerName"], "members": {"type": {}, "containerName": {}, "properties": {"type": "list", "member": {"shape": "S13"}}}}, "S5r": {"type": "structure", "required": ["sizeInGiB"], "members": {"sizeInGiB": {"type": "integer"}}}, "S5s": {"type": "list", "member": {"type": "structure", "members": {"arn": {}, "reason": {}, "detail": {}}}}, "S5y": {"type": "structure", "members": {"containerInstanceArn": {}, "ec2InstanceId": {}, "capacityProviderName": {}, "version": {"type": "long"}, "versionInfo": {"shape": "S60"}, "remainingResources": {"shape": "S61"}, "registeredResources": {"shape": "S61"}, "status": {}, "statusReason": {}, "agentConnected": {"type": "boolean"}, "runningTasksCount": {"type": "integer"}, "pendingTasksCount": {"type": "integer"}, "agentUpdateStatus": {}, "attributes": {"shape": "S3c"}, "registeredAt": {"type": "timestamp"}, "attachments": {"shape": "S14"}, "tags": {"shape": "Sb"}, "healthStatus": {"type": "structure", "members": {"overallStatus": {}, "details": {"type": "list", "member": {"type": "structure", "members": {"type": {}, "status": {}, "lastUpdated": {"type": "timestamp"}, "lastStatusChange": {"type": "timestamp"}}}}}}}}, "S60": {"type": "structure", "members": {"agentVersion": {}, "agentHash": {}, "dockerVersion": {}}}, "S61": {"type": "list", "member": {"type": "structure", "members": {"name": {}, "type": {}, "doubleValue": {"type": "double"}, "longValue": {"type": "long"}, "integerValue": {"type": "integer"}, "stringSetValue": {"shape": "St"}}}}, "S6p": {"type": "list", "member": {"shape": "S5y"}}, "S77": {"type": "list", "member": {"shape": "S78"}}, "S78": {"type": "structure", "members": {"attachments": {"shape": "S14"}, "attributes": {"shape": "S3c"}, "availabilityZone": {}, "capacityProviderName": {}, "clusterArn": {}, "connectivity": {}, "connectivityAt": {"type": "timestamp"}, "containerInstanceArn": {}, "containers": {"type": "list", "member": {"type": "structure", "members": {"containerArn": {}, "taskArn": {}, "name": {}, "image": {}, "imageDigest": {}, "runtimeId": {}, "lastStatus": {}, "exitCode": {"type": "integer"}, "reason": {}, "networkBindings": {"shape": "S7c"}, "networkInterfaces": {"type": "list", "member": {"type": "structure", "members": {"attachmentId": {}, "privateIpv4Address": {}, "ipv6Address": {}}}}, "healthStatus": {}, "managedAgents": {"type": "list", "member": {"type": "structure", "members": {"lastStartedAt": {"type": "timestamp"}, "name": {}, "reason": {}, "lastStatus": {}}}}, "cpu": {}, "memory": {}, "memoryReservation": {}, "gpuIds": {"type": "list", "member": {}}}}}, "cpu": {}, "createdAt": {"type": "timestamp"}, "desiredStatus": {}, "enableExecuteCommand": {"type": "boolean"}, "executionStoppedAt": {"type": "timestamp"}, "group": {}, "healthStatus": {}, "inferenceAccelerators": {"shape": "S5k"}, "lastStatus": {}, "launchType": {}, "memory": {}, "overrides": {"shape": "S7l"}, "platformVersion": {}, "platformFamily": {}, "pullStartedAt": {"type": "timestamp"}, "pullStoppedAt": {"type": "timestamp"}, "startedAt": {"type": "timestamp"}, "startedBy": {}, "stopCode": {}, "stoppedAt": {"type": "timestamp"}, "stoppedReason": {}, "stoppingAt": {"type": "timestamp"}, "tags": {"shape": "Sb"}, "taskArn": {}, "taskDefinitionArn": {}, "version": {"type": "long"}, "ephemeralStorage": {"shape": "S5r"}, "fargateEphemeralStorage": {"type": "structure", "members": {"sizeInGiB": {"type": "integer"}, "kmsKeyId": {}}}}}, "S7c": {"type": "list", "member": {"type": "structure", "members": {"bindIP": {}, "containerPort": {"type": "integer"}, "hostPort": {"type": "integer"}, "protocol": {}, "containerPortRange": {}, "hostPortRange": {}}}}, "S7l": {"type": "structure", "members": {"containerOverrides": {"type": "list", "member": {"type": "structure", "members": {"name": {}, "command": {"shape": "St"}, "environment": {"shape": "S3z"}, "environmentFiles": {"shape": "S40"}, "cpu": {"type": "integer"}, "memory": {"type": "integer"}, "memoryReservation": {"type": "integer"}, "resourceRequirements": {"shape": "S4r"}}}}, "cpu": {}, "inferenceAcceleratorOverrides": {"type": "list", "member": {"type": "structure", "members": {"deviceName": {}, "deviceType": {}}}}, "executionRoleArn": {}, "memory": {}, "taskRoleArn": {}, "ephemeralStorage": {"shape": "S5r"}}}, "S80": {"type": "list", "member": {"type": "structure", "members": {"taskArn": {}, "protectionEnabled": {"type": "boolean"}, "expirationDate": {"type": "timestamp"}}}}, "S97": {"type": "list", "member": {"type": "structure", "required": ["name"], "members": {"name": {}, "managedEBSVolume": {"type": "structure", "required": ["roleArn"], "members": {"encrypted": {"type": "boolean"}, "kmsKeyId": {}, "volumeType": {}, "sizeInGiB": {"type": "integer"}, "snapshotId": {}, "iops": {"type": "integer"}, "throughput": {"type": "integer"}, "tagSpecifications": {"shape": "S2i"}, "roleArn": {}, "terminationPolicy": {"type": "structure", "required": ["deleteOnTermination"], "members": {"deleteOnTermination": {"type": "boolean"}}}, "filesystemType": {}}}}}}, "S9h": {"type": "list", "member": {"type": "structure", "required": ["attachmentArn", "status"], "members": {"attachmentArn": {}, "status": {}}}}}}