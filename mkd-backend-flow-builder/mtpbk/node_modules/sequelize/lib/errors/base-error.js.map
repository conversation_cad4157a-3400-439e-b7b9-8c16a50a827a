{"version": 3, "sources": ["../../src/errors/base-error.ts"], "sourcesContent": ["export interface ErrorOptions {\n  stack?: string;\n}\n\nexport interface CommonErrorProperties {\n  /** The database specific error which triggered this one */\n  readonly parent: Error;\n\n  /** The database specific error which triggered this one */\n  readonly original: Error;\n\n  /** The SQL that triggered the error */\n  readonly sql: string;\n}\n\n/**\n * The Base Error all Sequelize Errors inherit from.\n *\n * Sequelize provides a host of custom error classes, to allow you to do easier debugging. All of these errors are exposed on the sequelize object and the sequelize constructor.\n * All sequelize errors inherit from the base JS error object.\n *\n * This means that errors can be accessed using `Sequelize.ValidationError`\n */\nabstract class BaseError extends Error {\n  constructor(message?: string) {\n    super(message);\n    this.name = 'SequelizeBaseError';\n  }\n}\n\nexport default BaseError;\n"], "mappings": ";;;;;;;AAAA;AAAA;AAAA;AAuBA,wBAAiC,MAAM;AAAA,EACrC,YAAY,SAAkB;AAC5B,UAAM;AACN,SAAK,OAAO;AAAA;AAAA;AAIhB,IAAO,qBAAQ;", "names": []}