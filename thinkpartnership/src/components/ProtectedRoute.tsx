import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import AuthAPI from '../services/AuthAPI';

interface ProtectedRouteProps {
  children: React.ReactNode;
  redirectTo?: string;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ 
  children, 
  redirectTo = '/customer/login' 
}) => {
  const location = useLocation();
  const isAuthenticated = AuthAPI.isAuthenticated();

  if (!isAuthenticated) {
    // Redirect to login page with return url
    return <Navigate to={redirectTo} state={{ from: location }} replace />;
  }

  return <>{children}</>;
};

export default ProtectedRoute;
