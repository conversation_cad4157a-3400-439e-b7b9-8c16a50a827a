import axios from 'axios';

// API Base URL
const API_BASE_URL = 'http://127.0.0.1:5172';

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('marketplace_token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
apiClient.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('marketplace_token');
      localStorage.removeItem('marketplace_user');
      window.location.href = '/customer/login';
    }
    return Promise.reject(error);
  }
);

export default apiClient;
export { API_BASE_URL };
