import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

import { MarketplaceLayout } from "@/components/MarketplaceLayout";
import MarketplaceAPI from "../../services/MarketplaceAPI";
import { toast } from "sonner";
import {
  Search,
  Home,
  Hammer,
  Leaf,
  Lightbulb,
  Truck,
  Building2,
  CheckCircle,
  Clock,
  Shield,
  Users,
  ArrowRight,
  Loader2
} from "lucide-react";

const CustomerBrowseServicesPage = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState("");
  const [categories, setCategories] = useState([]);
  const [services, setServices] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Load data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      // Load categories and featured services
      const [categoriesResult, servicesResult] = await Promise.all([
        MarketplaceAPI.getCategories(),
        MarketplaceAPI.getServices({ limit: 6 }) // Get first 6 services as featured
      ]);

      if (categoriesResult.success) {
        setCategories(categoriesResult.categories);
      } else {
        console.error('Failed to load categories:', categoriesResult.message);
      }

      if (servicesResult.success) {
        setServices(servicesResult.services);
      } else {
        console.error('Failed to load services:', servicesResult.message);
      }
    } catch (error) {
      console.error('Error loading data:', error);
      setError('Failed to load data. Please try again.');
      toast.error('Failed to load marketplace data');
    } finally {
      setLoading(false);
    }
  };

  const handleSearch = () => {
    // Navigate to all services with search parameters
    const params = new URLSearchParams();
    if (searchTerm) params.set('search', searchTerm);
    navigate(`/marketplace/services?${params.toString()}`);
  };

  const handleViewDetails = (serviceId: number) => {
    // Navigate to the marketplace service details page
    navigate(`/marketplace/services/${serviceId}`);
  };

  const handleCategoryClick = (categoryId: number) => {
    // Navigate to services filtered by category
    navigate(`/marketplace/services?category_id=${categoryId}`);
  };

  // Helper function to get category icon and color
  const getCategoryIcon = (categoryName: string) => {
    const iconMap = {
      'plumbing': { icon: CheckCircle, color: 'bg-blue-500' },
      'electrical': { icon: Lightbulb, color: 'bg-yellow-500' },
      'hvac': { icon: Home, color: 'bg-orange-500' },
      'roofing': { icon: Building2, color: 'bg-gray-600' },
      'flooring': { icon: Leaf, color: 'bg-amber-600' },
      'painting': { icon: Truck, color: 'bg-purple-500' },
      'landscaping': { icon: Leaf, color: 'bg-green-600' },
      'handyman': { icon: Hammer, color: 'bg-gray-500' },
      'default': { icon: Building2, color: 'bg-gray-400' }
    };

    const key = categoryName.toLowerCase();
    return iconMap[key] || iconMap.default;
  };



  return (
    <MarketplaceLayout>
      <div className="min-h-screen bg-white">
        {/* Hero Section - Modern Marketplace Style */}
        <section className="relative bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 pt-8">
        <div className="max-w-7xl mx-auto px-4 py-20">
          <div className="text-center mb-16">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-bold mb-8 text-gray-900 leading-tight">
              Find the perfect
              <span className="bg-gradient-to-r from-green-600 to-emerald-600 bg-clip-text text-transparent">
                {" "}service{" "}
              </span>
              for your home
            </h1>
            <p className="text-xl md:text-2xl text-gray-600 mb-12 max-w-3xl mx-auto leading-relaxed">
              Connect with trusted professionals. Quality guaranteed, satisfaction promised.
            </p>

            {/* Enhanced Search Bar */}
            <div className="max-w-4xl mx-auto mb-12">
              <div className="relative group">
                <div className="absolute inset-y-0 left-0 pl-6 flex items-center pointer-events-none">
                  <Search className="h-6 w-6 text-gray-400 group-focus-within:text-green-500 transition-colors" />
                </div>
                <Input
                  type="search"
                  placeholder="What service do you need? Try 'home cleaning', 'plumbing repair', 'painting'..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  onKeyDown={(e) => e.key === 'Enter' && handleSearch()}
                  className="h-16 pl-16 pr-40 text-lg bg-white border-2 border-gray-200 rounded-2xl shadow-lg focus:border-green-500 focus:ring-4 focus:ring-green-100 text-gray-900 placeholder:text-gray-500 transition-all duration-300"
                />
                <Button
                  onClick={handleSearch}
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 h-10 px-8 bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 rounded-xl text-white font-semibold shadow-lg hover:shadow-xl transition-all duration-300"
                >
                  Search
                </Button>
              </div>
            </div>

            {/* Popular Searches */}
            <div className="flex flex-wrap justify-center gap-4 mb-12">
              <span className="text-gray-500 text-base font-medium">Popular:</span>
              {["Plumbing", "Electrical", "HVAC", "Roofing", "Flooring", "Painting"].map((term) => (
                <Button
                  key={term}
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    setSearchTerm(term);
                    handleSearch();
                  }}
                  className="rounded-full border-gray-300 text-gray-600 hover:border-green-500 hover:text-green-600 hover:bg-green-50 transition-all duration-300 px-4 py-2"
                >
                  {term}
                </Button>
              ))}
            </div>

            {/* Trust Indicators */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
              <div className="flex items-center justify-center gap-3 text-gray-700">
                <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                  <Shield className="h-6 w-6 text-green-600" />
                </div>
                <div>
                  <div className="font-semibold">Verified Professionals</div>
                  <div className="text-sm text-gray-500">Background checked</div>
                </div>
              </div>
              <div className="flex items-center justify-center gap-3 text-gray-700">
                <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center">
                  <CheckCircle className="h-6 w-6 text-yellow-600" />
                </div>
                <div>
                  <div className="font-semibold">Quality Guaranteed</div>
                  <div className="text-sm text-gray-500">100% satisfaction</div>
                </div>
              </div>
              <div className="flex items-center justify-center gap-3 text-gray-700">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <Clock className="h-6 w-6 text-blue-600" />
                </div>
                <div>
                  <div className="font-semibold">Fast Response</div>
                  <div className="text-sm text-gray-500">Quick turnaround</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>



      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {/* Featured Services Section */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Featured Services
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              Top-rated services from trusted professionals in your area.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {loading ? (
              // Loading skeleton for services
              Array.from({ length: 6 }).map((_, index) => (
                <Card key={index} className="overflow-hidden border shadow-lg bg-white rounded-2xl">
                  <div className="h-56 bg-gray-200 animate-pulse"></div>
                  <CardContent className="p-6">
                    <div className="h-6 bg-gray-200 rounded mb-2 animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded mb-3 animate-pulse"></div>
                    <div className="h-4 bg-gray-200 rounded mb-4 animate-pulse"></div>
                    <div className="flex justify-between items-center">
                      <div className="h-6 bg-gray-200 rounded w-20 animate-pulse"></div>
                      <div className="h-8 bg-gray-200 rounded w-24 animate-pulse"></div>
                    </div>
                  </CardContent>
                </Card>
              ))
            ) : (
              services.map((service, index) => (
              <Card key={index} className="group overflow-hidden border shadow-lg hover:shadow-2xl transition-all duration-500 hover:-translate-y-2 bg-white rounded-2xl relative">

                <div className="relative overflow-hidden rounded-t-2xl">
                  <img
                    src={service.image_url || "https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop&crop=center"}
                    alt={service.title}
                    className="w-full h-56 object-cover group-hover:scale-110 transition-transform duration-700"
                  />

                  {/* Gradient Overlay */}
                  <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                  {/* Status Badge */}
                  {service.status === 1 && (
                    <div className="absolute top-4 right-4">
                      <Badge className="bg-green-500 text-white">
                        <CheckCircle className="w-3 h-3 mr-1" />
                        Active
                      </Badge>
                    </div>
                  )}
                </div>

                <CardContent className="p-6">
                  <div className="mb-4">
                    <h3 className="font-bold text-lg text-gray-900 mb-2 line-clamp-2 group-hover:text-green-600 transition-colors duration-300">
                      {service.title}
                    </h3>
                    <p className="text-sm text-gray-600 line-clamp-2 mb-3">
                      {service.description || service.short_description || 'Professional service available'}
                    </p>
                  </div>

                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-1 text-xs text-gray-500">
                      <Clock className="w-3 h-3" />
                      <span>Quick response</span>
                    </div>
                    {service.rating && (
                      <div className="flex items-center space-x-1 text-xs text-gray-500">
                        <span>⭐ {service.rating}</span>
                      </div>
                    )}
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <div className="flex items-center space-x-2 mb-1">
                        <span className="text-xl font-bold text-gray-900">
                          ${service.base_price || service.price || '0'}
                        </span>
                      </div>
                      <span className="text-xs text-gray-500">starting price</span>
                    </div>
                    <Button
                      size="sm"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleViewDetails(service.id);
                      }}
                      className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white rounded-full px-6 shadow-lg hover:shadow-xl transition-all duration-300"
                    >
                      View Details
                    </Button>
                  </div>
                </CardContent>
              </Card>
              ))
            )}
          </div>

          {/* Error State */}
          {error && (
            <div className="text-center py-12">
              <p className="text-red-600 mb-4">{error}</p>
              <Button onClick={loadData} variant="outline">
                Try Again
              </Button>
            </div>
          )}

          {/* Empty State */}
          {!loading && !error && services.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-600 mb-4">No services available at the moment.</p>
              <Button onClick={loadData} variant="outline">
                Refresh
              </Button>
            </div>
          )}

          {/* Load More Button */}
          <div className="text-center mt-12">
            <Button
              variant="outline"
              size="lg"
              className="rounded-full border-2 border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300 px-8 py-3 text-lg font-semibold"
              onClick={() => navigate('/marketplace/services')}
            >
              View All Services
            </Button>
          </div>
        </section>

        {/* Enhanced Categories Section */}
        {/* <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Explore Popular Categories
            </h2>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              Find the perfect professional for any home service you need
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-8 gap-6">
            {loading ? (
              // Loading skeleton for categories
              Array.from({ length: 8 }).map((_, index) => (
                <Card key={index} className="border-0 bg-white rounded-2xl overflow-hidden">
                  <CardContent className="p-6 text-center">
                    <div className="w-16 h-16 bg-gray-200 rounded-2xl flex items-center justify-center mx-auto mb-4 animate-pulse">
                      <Loader2 className="h-8 w-8 text-gray-400 animate-spin" />
                    </div>
                    <div className="h-4 bg-gray-200 rounded mb-2 animate-pulse"></div>
                    <div className="h-3 bg-gray-200 rounded mb-2 animate-pulse"></div>
                    <div className="h-5 bg-gray-200 rounded animate-pulse"></div>
                  </CardContent>
                </Card>
              ))
            ) : (
              categories.map((category) => {
                const { icon: IconComponent, color } = getCategoryIcon(category.name);
                return (
                  <Card
                    key={category.id}
                    className="group cursor-pointer transition-all duration-500 hover:shadow-2xl border-0 bg-white hover:bg-gradient-to-br hover:from-white hover:to-gray-50 rounded-2xl overflow-hidden"
                    onClick={() => handleCategoryClick(category.id)}
                  >
                    <CardContent className="p-6 text-center">
                      <div className={`w-16 h-16 ${color} rounded-2xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 group-hover:rotate-3 transition-all duration-500 shadow-lg`}>
                        <IconComponent className="h-8 w-8 text-white" />
                      </div>
                      <h3 className="font-semibold text-base mb-2 text-gray-900 group-hover:text-green-600 transition-colors duration-300">
                        {category.name}
                      </h3>
                      <p className="text-sm text-gray-500 mb-2">
                        {category.description || 'Professional services'}
                      </p>
                      <Badge variant="secondary" className="text-xs bg-gray-100 text-gray-600">
                        {category.service_count || 0} services
                      </Badge>
                    </CardContent>
                  </Card>
                );
              })
            )}
          </div>

          <div className="text-center mt-12">
            <Button
              variant="outline"
              size="lg"
              className="rounded-full border-green-600 text-green-600 hover:bg-green-600 hover:text-white transition-all duration-300 px-8"
              onClick={() => navigate('/marketplace/categories')}
            >
              View All Categories
              <ArrowRight className="ml-2 w-5 h-5" />
            </Button>
          </div>
        </section> */}



        {/* Enhanced Call-to-Action Section */}
        <section className="mb-20">
          <div className="bg-gradient-to-br from-green-50 via-emerald-50 to-teal-50 rounded-3xl p-12 text-center">
            <div className="max-w-4xl mx-auto">
              <div className="flex items-center justify-center mb-6">
                <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                  <Users className="h-8 w-8 text-green-600" />
                </div>
              </div>
              <h3 className="text-3xl md:text-4xl font-bold text-gray-900 mb-6">
                Ready to Get Started?
              </h3>
              <p className="text-lg text-gray-600 mb-8 max-w-2xl mx-auto leading-relaxed">
                Connect with service providers in your area. Quality work from trusted professionals.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 rounded-full px-8 py-3 shadow-lg hover:shadow-xl transition-all duration-300"
                  onClick={() => navigate('/marketplace/services')}
                >
                  Browse All Services
                </Button>
              </div>
            </div>
          </div>
        </section>


      </div>
    </div>
    </MarketplaceLayout>
  );
};

export default CustomerBrowseServicesPage;