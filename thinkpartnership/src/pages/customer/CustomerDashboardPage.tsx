import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { MarketplaceLayout } from "@/components/MarketplaceLayout";
import AuthAPI from "../../services/AuthAPI";
import MarketplaceAPI from "../../services/MarketplaceAPI";
import { toast } from "sonner";
import {
  User,
  FileText,
  ShoppingCart,
  Settings,
  LogOut,
  CheckCircle,
  Clock,
  AlertCircle,
  Loader2,
  Plus,
  ArrowRight
} from "lucide-react";

const CustomerDashboardPage = () => {
  const navigate = useNavigate();
  const [user, setUser] = useState(null);
  const [stats, setStats] = useState({
    totalRequests: 0,
    pendingRequests: 0,
    completedRequests: 0,
    totalOrders: 0
  });
  const [recentRequests, setRecentRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  useEffect(() => {
    // Check authentication
    if (!AuthAPI.isAuthenticated()) {
      navigate('/customer/login');
      return;
    }

    loadDashboardData();
  }, [navigate]);

  const loadDashboardData = async () => {
    setLoading(true);
    try {
      // Load user profile
      const profileResult = await AuthAPI.getProfile();
      if (profileResult.success) {
        setUser(profileResult.user);
      }

      // Load service requests
      const requestsResult = await MarketplaceAPI.getServiceRequests();
      if (requestsResult.success) {
        const requests = requestsResult.requests;
        setRecentRequests(requests.slice(0, 3)); // Show only recent 3

        // Calculate stats
        const totalRequests = requests.length;
        const pendingRequests = requests.filter(r => r.status === 0).length;
        const completedRequests = requests.filter(r => r.status === 3).length;

        setStats(prev => ({
          ...prev,
          totalRequests,
          pendingRequests,
          completedRequests
        }));
      }

      // Load orders
      const ordersResult = await MarketplaceAPI.getOrders();
      if (ordersResult.success) {
        setStats(prev => ({
          ...prev,
          totalOrders: ordersResult.orders.length
        }));
      }

    } catch (error) {
      console.error('Error loading dashboard data:', error);
      setError('Failed to load dashboard data');
      toast.error('Failed to load dashboard data');
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    AuthAPI.logout();
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case 0: return { label: "Pending", color: "bg-yellow-100 text-yellow-800", icon: <Clock className="w-3 h-3" /> };
      case 1: return { label: "Accepted", color: "bg-blue-100 text-blue-800", icon: <CheckCircle className="w-3 h-3" /> };
      case 2: return { label: "In Progress", color: "bg-purple-100 text-purple-800", icon: <Clock className="w-3 h-3" /> };
      case 3: return { label: "Completed", color: "bg-green-100 text-green-800", icon: <CheckCircle className="w-3 h-3" /> };
      case 4: return { label: "Cancelled", color: "bg-red-100 text-red-800", icon: <AlertCircle className="w-3 h-3" /> };
      default: return { label: "Unknown", color: "bg-gray-100 text-gray-800", icon: <AlertCircle className="w-3 h-3" /> };
    }
  };

  if (loading) {
    return (
      <MarketplaceLayout>
        <div className="min-h-screen bg-gray-50 flex items-center justify-center">
          <div className="text-center">
            <Loader2 className="w-8 h-8 animate-spin mx-auto mb-4 text-green-600" />
            <p className="text-gray-600">Loading your dashboard...</p>
          </div>
        </div>
      </MarketplaceLayout>
    );
  }

  return (
    <MarketplaceLayout>
      <div className="min-h-screen bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 py-8">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900">
                  Welcome back{user ? `, ${user.firstName}` : ''}!
                </h1>
                <p className="text-gray-600 mt-1">
                  Manage your service requests and orders from your dashboard
                </p>
              </div>
              <div className="flex items-center gap-4">
                <Button
                  variant="outline"
                  onClick={() => navigate('/marketplace/profile')}
                  className="flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  Settings
                </Button>
                <Button
                  variant="outline"
                  onClick={handleLogout}
                  className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
                >
                  <LogOut className="w-4 h-4" />
                  Logout
                </Button>
              </div>
            </div>
          </div>

          {/* Error Alert */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Stats Cards */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total Requests</p>
                    <p className="text-2xl font-bold text-gray-900">{stats.totalRequests}</p>
                  </div>
                  <FileText className="w-8 h-8 text-blue-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-2xl font-bold text-yellow-600">{stats.pendingRequests}</p>
                  </div>
                  <Clock className="w-8 h-8 text-yellow-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Completed</p>
                    <p className="text-2xl font-bold text-green-600">{stats.completedRequests}</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Orders</p>
                    <p className="text-2xl font-bold text-purple-600">{stats.totalOrders}</p>
                  </div>
                  <ShoppingCart className="w-8 h-8 text-purple-600" />
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => navigate('/marketplace')}>
              <CardContent className="p-6 text-center">
                <Plus className="w-12 h-12 text-green-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Browse Services</h3>
                <p className="text-gray-600 text-sm">Find and request new services</p>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => navigate('/marketplace/requests')}>
              <CardContent className="p-6 text-center">
                <FileText className="w-12 h-12 text-blue-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">My Requests</h3>
                <p className="text-gray-600 text-sm">View all service requests</p>
              </CardContent>
            </Card>

            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={() => navigate('/marketplace/orders')}>
              <CardContent className="p-6 text-center">
                <ShoppingCart className="w-12 h-12 text-purple-600 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">My Orders</h3>
                <p className="text-gray-600 text-sm">Track your orders</p>
              </CardContent>
            </Card>
          </div>

          {/* Recent Requests */}
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle>Recent Service Requests</CardTitle>
                  <CardDescription>Your latest service requests</CardDescription>
                </div>
                <Button
                  variant="outline"
                  onClick={() => navigate('/marketplace/requests')}
                  className="flex items-center gap-2"
                >
                  View All
                  <ArrowRight className="w-4 h-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {recentRequests.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-600">No service requests yet</p>
                  <Button
                    onClick={() => navigate('/marketplace')}
                    className="mt-4 bg-green-600 hover:bg-green-700"
                  >
                    Browse Services
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentRequests.map((request) => {
                    const statusInfo = getStatusInfo(request.status);
                    return (
                      <div key={request.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">
                            Request #{request.request_number || request.id}
                          </h4>
                          <p className="text-sm text-gray-600 mt-1">{request.description}</p>
                          <p className="text-xs text-gray-500 mt-1">
                            Created: {new Date(request.created_at).toLocaleDateString()}
                          </p>
                        </div>
                        <Badge className={`${statusInfo.color} flex items-center gap-1`}>
                          {statusInfo.icon}
                          {statusInfo.label}
                        </Badge>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </MarketplaceLayout>
  );
};

export default CustomerDashboardPage;
