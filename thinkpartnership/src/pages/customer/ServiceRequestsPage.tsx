import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MarketplaceLayout } from "@/components/MarketplaceLayout";
import MarketplaceAPI from "../../services/MarketplaceAPI";
import AuthAPI from "../../services/AuthAPI";
import { toast } from "sonner";
import {
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  MessageCircle,
  Phone,
  Calendar,
  MapPin,
  User,
  AlertCircle,
  Trash2,
  Loader2
} from "lucide-react";

const ServiceRequestsPage = () => {
  const navigate = useNavigate();
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Check authentication
  useEffect(() => {
    if (!AuthAPI.isAuthenticated()) {
      navigate('/customer/login');
      return;
    }
    loadServiceRequests();
  }, [navigate]);

  const loadServiceRequests = async () => {
    setLoading(true);
    try {
      const result = await MarketplaceAPI.getServiceRequests();
      if (result.success) {
        setRequests(result.requests);
      } else {
        setError(result.message);
        toast.error('Failed to load service requests');
      }
    } catch (error) {
      console.error('Error loading service requests:', error);
      setError('Failed to load service requests');
      toast.error('Failed to load service requests');
    } finally {
      setLoading(false);
    }
  };



  const getStatusInfo = (status: number) => {
    switch (status) {
      case 0: // Pending
        return {
          label: "Pending",
          color: "bg-yellow-100 text-yellow-800",
          icon: <Clock className="w-4 h-4" />
        };
      case 1: // Accepted
        return {
          label: "Accepted",
          color: "bg-blue-100 text-blue-800",
          icon: <CheckCircle className="w-4 h-4" />
        };
      case 2: // In Progress
        return {
          label: "In Progress",
          color: "bg-purple-100 text-purple-800",
          icon: <Clock className="w-4 h-4" />
        };
      case 3: // Completed
        return {
          label: "Completed",
          color: "bg-green-100 text-green-800",
          icon: <CheckCircle className="w-4 h-4" />
        };
      case 4: // Cancelled
        return {
          label: "Cancelled",
          color: "bg-red-100 text-red-800",
          icon: <XCircle className="w-4 h-4" />
        };
      case 5: // Rejected
        return {
          label: "Rejected",
          color: "bg-red-100 text-red-800",
          icon: <XCircle className="w-4 h-4" />
        };
      case 6: // Expired
        return {
          label: "Expired",
          color: "bg-gray-100 text-gray-800",
          icon: <AlertCircle className="w-4 h-4" />
        };
      default:
        return {
          label: "Unknown",
          color: "bg-gray-100 text-gray-800",
          icon: <AlertCircle className="w-4 h-4" />
        };
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case "urgent":
        return "bg-red-500";
      case "normal":
        return "bg-green-500";
      default:
        return "bg-gray-500";
    }
  };

  const handleViewDetails = (requestId: number) => {
    navigate(`/marketplace/requests/${requestId}`);
  };

  const handleChatWithVendor = (vendorName: string) => {
    navigate(`/marketplace/chat/${encodeURIComponent(vendorName)}`);
  };

  const handleDeleteRequest = (requestId: number) => {
    setRequests(requests.filter(req => req.id !== requestId));
  };

  const pendingCount = requests.filter(req => req.status === "pending").length;
  const confirmedCount = requests.filter(req => req.status === "confirmed").length;

  return (
    <MarketplaceLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <section className="bg-white border-b border-gray-200 py-8">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Service Requests</h1>
                  <p className="text-gray-600">{requests.length} total requests</p>
                </div>
              </div>
              
              <Button 
                onClick={() => navigate('/marketplace')}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Request New Service
              </Button>
            </div>

            {/* Quick Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-yellow-600">{pendingCount}</div>
                <div className="text-sm text-yellow-700">Pending</div>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">{confirmedCount}</div>
                <div className="text-sm text-blue-700">Confirmed</div>
              </div>
              <div className="bg-green-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-green-600">
                  {requests.filter(req => req.status === "completed").length}
                </div>
                <div className="text-sm text-green-700">Completed</div>
              </div>
              <div className="bg-red-50 p-4 rounded-lg">
                <div className="text-2xl font-bold text-red-600">
                  {requests.filter(req => req.status === "cancelled").length}
                </div>
                <div className="text-sm text-red-700">Cancelled</div>
              </div>
            </div>
          </div>
        </section>

        <div className="max-w-7xl mx-auto px-4 py-8">
          {requests.length === 0 ? (
            /* Empty State */
            <div className="text-center py-16">
              <div className="w-24 h-24 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <FileText className="w-12 h-12 text-gray-400" />
              </div>
              <h2 className="text-2xl font-bold text-gray-900 mb-4">No service requests</h2>
              <p className="text-gray-600 mb-8">You haven't requested any services yet. Browse our marketplace to get started.</p>
              <Button 
                onClick={() => navigate('/marketplace')}
                className="bg-blue-600 hover:bg-blue-700"
              >
                Browse Services
              </Button>
            </div>
          ) : (
            /* Requests List */
            <div className="space-y-6">
              {loading ? (
                // Loading skeleton
                Array.from({ length: 3 }).map((_, index) => (
                  <Card key={index} className="border border-gray-200">
                    <CardContent className="p-6">
                      <div className="flex gap-4">
                        <div className="w-16 h-16 rounded-full bg-gray-200 animate-pulse"></div>
                        <div className="flex-1">
                          <div className="h-6 bg-gray-200 rounded mb-2 animate-pulse"></div>
                          <div className="h-4 bg-gray-200 rounded mb-4 animate-pulse"></div>
                          <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                ))
              ) : (
                requests.map((request) => {
                  const statusInfo = getStatusInfo(request.status);
                  return (
                    <Card key={request.id} className="border border-gray-200 hover:shadow-lg transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex gap-4">
                          <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center">
                            <FileText className="w-8 h-8 text-gray-400" />
                          </div>

                          <div className="flex-1">
                            <div className="flex items-start justify-between mb-3">
                              <div>
                                <h3 className="text-xl font-bold text-gray-900 mb-1">
                                  Request #{request.request_number || request.id}
                                </h3>
                                <div className="flex items-center gap-2 mb-2">
                                  <FileText className="w-4 h-4 text-gray-500" />
                                  <span className="text-gray-700">Service Request</span>
                                  {request.urgency && (
                                    <>
                                      <div className={`w-2 h-2 rounded-full ${getUrgencyColor(request.urgency)}`}></div>
                                      <span className="text-sm text-gray-500 capitalize">{request.urgency}</span>
                                    </>
                                  )}
                                </div>
                              </div>

                              <div className="flex items-center gap-2">
                                <Badge className={`${statusInfo.color} flex items-center gap-1`}>
                                  {statusInfo.icon}
                                  <span>{statusInfo.label}</span>
                                </Badge>
                                <Button
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleDeleteRequest(request.id)}
                                  className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                >
                                  <Trash2 className="w-4 h-4" />
                                </Button>
                              </div>
                            </div>

                            <p className="text-gray-600 mb-4">{request.description}</p>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                          <div className="flex items-center gap-2 text-sm text-gray-600">
                            <Calendar className="w-4 h-4" />
                            <span>Created: {new Date(request.created_at).toLocaleDateString()}</span>
                          </div>
                          {request.preferred_date && (
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <Clock className="w-4 h-4" />
                              <span>Preferred: {new Date(request.preferred_date).toLocaleDateString()}</span>
                            </div>
                          )}
                          {request.location && (
                            <div className="flex items-center gap-2 text-sm text-gray-600">
                              <MapPin className="w-4 h-4" />
                              <span>{request.location}</span>
                            </div>
                          )}
                        </div>

                        <div className="flex items-center justify-between">
                          {request.budget_range && (
                            <div className="text-lg font-semibold text-gray-900">
                              Budget: {request.budget_range}
                            </div>
                          )}
                          
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => handleViewDetails(request.id)}
                            >
                              View Details
                            </Button>
                            
                            {(request.status === 0 || request.status === 1) && (
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => handleChatWithVendor(request.id)}
                                className="border-blue-600 text-blue-600 hover:bg-blue-50"
                              >
                                <MessageCircle className="w-4 h-4 mr-2" />
                                Chat
                              </Button>
                            )}

                            {request.status === 1 && (
                              <Button
                                size="sm"
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <Phone className="w-4 h-4 mr-2" />
                                Call
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Error State */}
            {error && (
              <div className="text-center py-12">
                <AlertCircle className="w-16 h-16 text-red-500 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">Error Loading Requests</h3>
                <p className="text-red-600 mb-4">{error}</p>
                <Button onClick={loadServiceRequests} variant="outline">
                  Try Again
                </Button>
              </div>
            )}

            {/* Empty State */}
            {!loading && !error && requests.length === 0 && (
              <div className="text-center py-12">
                <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-gray-900 mb-2">No Service Requests</h3>
                <p className="text-gray-600 mb-4">You haven't made any service requests yet.</p>
                <Button onClick={() => navigate('/marketplace')} className="bg-green-600 hover:bg-green-700">
                  Browse Services
                </Button>
              </div>
            )}
          )}
        </div>

        {/* Help Section */}
        {requests.length > 0 && (
          <section className="bg-white border-t border-gray-200 py-8">
            <div className="max-w-7xl mx-auto px-4 text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Need Help with Your Requests?
              </h3>
              <p className="text-gray-600 mb-6">
                Our support team is here to help you manage your service requests
              </p>
              <Button 
                variant="outline"
                onClick={() => navigate('/marketplace/help')}
                className="border-gray-300 text-gray-700 hover:bg-gray-50"
              >
                Contact Support
              </Button>
            </div>
          </section>
        )}
      </div>
    </MarketplaceLayout>
  );
};

export default ServiceRequestsPage;
