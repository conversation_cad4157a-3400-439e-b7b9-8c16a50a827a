import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { MarketplaceLayout } from "@/components/MarketplaceLayout";
import MarketplaceAPI from "../../services/MarketplaceAPI";
import AuthAPI from "../../services/AuthAPI";
import { toast } from "sonner";
import {
  FileText,
  Clock,
  CheckCircle,
  XCircle,
  MessageCircle,
  Calendar,
  MapPin,
  AlertCircle,
  Trash2,
  Loader2,
  Plus
} from "lucide-react";

const ServiceRequestsPage = () => {
  const navigate = useNavigate();
  const [requests, setRequests] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState("");

  // Check authentication
  useEffect(() => {
    if (!AuthAPI.isAuthenticated()) {
      navigate('/customer/login');
      return;
    }
    loadServiceRequests();
  }, [navigate]);

  const loadServiceRequests = async () => {
    setLoading(true);
    try {
      const result = await MarketplaceAPI.getServiceRequests();
      if (result.success) {
        setRequests(result.requests);
      } else {
        setError(result.message);
        toast.error('Failed to load service requests');
      }
    } catch (error) {
      console.error('Error loading service requests:', error);
      setError('Failed to load service requests');
      toast.error('Failed to load service requests');
    } finally {
      setLoading(false);
    }
  };

  const getStatusInfo = (status) => {
    switch (status) {
      case 0: return { label: "Pending", color: "bg-yellow-100 text-yellow-800", icon: <Clock className="w-3 h-3" /> };
      case 1: return { label: "Accepted", color: "bg-blue-100 text-blue-800", icon: <CheckCircle className="w-3 h-3" /> };
      case 2: return { label: "In Progress", color: "bg-purple-100 text-purple-800", icon: <Clock className="w-3 h-3" /> };
      case 3: return { label: "Completed", color: "bg-green-100 text-green-800", icon: <CheckCircle className="w-3 h-3" /> };
      case 4: return { label: "Cancelled", color: "bg-red-100 text-red-800", icon: <XCircle className="w-3 h-3" /> };
      default: return { label: "Unknown", color: "bg-gray-100 text-gray-800", icon: <AlertCircle className="w-3 h-3" /> };
    }
  };

  const handleViewDetails = (requestId) => {
    navigate(`/marketplace/requests/${requestId}`);
  };

  const handleDeleteRequest = (requestId) => {
    setRequests(requests.filter(req => req.id !== requestId));
    toast.success('Request deleted successfully');
  };

  const pendingCount = requests.filter(req => req.status === 0).length;
  const acceptedCount = requests.filter(req => req.status === 1).length;
  const completedCount = requests.filter(req => req.status === 3).length;

  return (
    <MarketplaceLayout>
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <section className="bg-white border-b border-gray-200 py-8">
          <div className="max-w-7xl mx-auto px-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                  <FileText className="w-6 h-6 text-blue-600" />
                </div>
                <div>
                  <h1 className="text-3xl font-bold text-gray-900">Service Requests</h1>
                  <p className="text-gray-600">Track and manage your service requests</p>
                </div>
              </div>
              
              <Button 
                onClick={() => navigate('/marketplace')}
                className="bg-green-600 hover:bg-green-700"
              >
                <Plus className="w-4 h-4 mr-2" />
                New Request
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
              <div className="bg-white rounded-lg p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Total</p>
                    <p className="text-2xl font-bold text-gray-900">{requests.length}</p>
                  </div>
                  <FileText className="w-8 h-8 text-gray-600" />
                </div>
              </div>
              
              <div className="bg-white rounded-lg p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Pending</p>
                    <p className="text-2xl font-bold text-yellow-600">{pendingCount}</p>
                  </div>
                  <Clock className="w-8 h-8 text-yellow-600" />
                </div>
              </div>
              
              <div className="bg-white rounded-lg p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Accepted</p>
                    <p className="text-2xl font-bold text-blue-600">{acceptedCount}</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-blue-600" />
                </div>
              </div>
              
              <div className="bg-white rounded-lg p-6 border border-gray-200">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Completed</p>
                    <p className="text-2xl font-bold text-green-600">{completedCount}</p>
                  </div>
                  <CheckCircle className="w-8 h-8 text-green-600" />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Main Content */}
        <div className="max-w-7xl mx-auto px-4 py-8">
          {/* Error State */}
          {error && (
            <Alert variant="destructive" className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {/* Loading State */}
          {loading ? (
            <div className="space-y-6">
              {Array.from({ length: 3 }).map((_, index) => (
                <Card key={index} className="border border-gray-200">
                  <CardContent className="p-6">
                    <div className="flex gap-4">
                      <div className="w-16 h-16 rounded-full bg-gray-200 animate-pulse"></div>
                      <div className="flex-1">
                        <div className="h-6 bg-gray-200 rounded mb-2 animate-pulse"></div>
                        <div className="h-4 bg-gray-200 rounded mb-4 animate-pulse"></div>
                        <div className="h-4 bg-gray-200 rounded animate-pulse"></div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <>
              {/* Empty State */}
              {requests.length === 0 ? (
                <div className="text-center py-16">
                  <FileText className="w-16 h-16 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">No Service Requests</h3>
                  <p className="text-gray-600 mb-4">You haven't made any service requests yet.</p>
                  <Button onClick={() => navigate('/marketplace')} className="bg-green-600 hover:bg-green-700">
                    Browse Services
                  </Button>
                </div>
              ) : (
                /* Requests List */
                <div className="space-y-6">
                  {requests.map((request) => {
                    const statusInfo = getStatusInfo(request.status);
                    return (
                      <Card key={request.id} className="border border-gray-200 hover:shadow-lg transition-shadow">
                        <CardContent className="p-6">
                          <div className="flex gap-4">
                            <div className="w-16 h-16 rounded-full bg-gray-200 flex items-center justify-center">
                              <FileText className="w-8 h-8 text-gray-400" />
                            </div>
                            
                            <div className="flex-1">
                              <div className="flex items-start justify-between mb-3">
                                <div>
                                  <h3 className="text-xl font-bold text-gray-900 mb-1">
                                    Request #{request.request_number || request.id}
                                  </h3>
                                  <div className="flex items-center gap-2 mb-2">
                                    <FileText className="w-4 h-4 text-gray-500" />
                                    <span className="text-gray-700">Service Request</span>
                                  </div>
                                </div>
                                
                                <div className="flex items-center gap-2">
                                  <Badge className={`${statusInfo.color} flex items-center gap-1`}>
                                    {statusInfo.icon}
                                    <span>{statusInfo.label}</span>
                                  </Badge>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteRequest(request.id)}
                                    className="text-red-600 hover:text-red-700 hover:bg-red-50"
                                  >
                                    <Trash2 className="w-4 h-4" />
                                  </Button>
                                </div>
                              </div>

                              <p className="text-gray-600 mb-4">{request.description}</p>

                              <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div className="flex items-center gap-2 text-sm text-gray-600">
                                  <Calendar className="w-4 h-4" />
                                  <span>Created: {new Date(request.created_at).toLocaleDateString()}</span>
                                </div>
                                {request.preferred_date && (
                                  <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <Clock className="w-4 h-4" />
                                    <span>Preferred: {new Date(request.preferred_date).toLocaleDateString()}</span>
                                  </div>
                                )}
                                {request.location && (
                                  <div className="flex items-center gap-2 text-sm text-gray-600">
                                    <MapPin className="w-4 h-4" />
                                    <span>{request.location}</span>
                                  </div>
                                )}
                              </div>

                              <div className="flex items-center justify-between">
                                {request.budget_range && (
                                  <div className="text-lg font-semibold text-gray-900">
                                    Budget: {request.budget_range}
                                  </div>
                                )}
                                
                                <div className="flex gap-2">
                                  <Button
                                    variant="outline"
                                    size="sm"
                                    onClick={() => handleViewDetails(request.id)}
                                  >
                                    View Details
                                  </Button>
                                  
                                  {(request.status === 0 || request.status === 1) && (
                                    <Button
                                      variant="outline"
                                      size="sm"
                                      className="border-blue-600 text-blue-600 hover:bg-blue-50"
                                    >
                                      <MessageCircle className="w-4 h-4 mr-2" />
                                      Chat
                                    </Button>
                                  )}
                                </div>
                              </div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    );
                  })}
                </div>
              )}
            </>
          )}
        </div>

        {/* Help Section */}
        {requests.length > 0 && (
          <section className="bg-white border-t border-gray-200 py-8">
            <div className="max-w-7xl mx-auto px-4 text-center">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                Need Help with Your Requests?
              </h3>
              <p className="text-gray-600 mb-6">
                Our support team is here to help you manage your service requests
              </p>
              <Button variant="outline">
                Contact Support
              </Button>
            </div>
          </section>
        )}
      </div>
    </MarketplaceLayout>
  );
};

export default ServiceRequestsPage;
