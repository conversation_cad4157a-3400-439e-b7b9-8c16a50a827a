import apiClient from '../config/api';

class AuthAPI {
  /**
   * Register a new customer
   * @param {Object} userData - User registration data
   * @returns {Promise} API response
   */
  static async register(userData) {
    try {
      const response = await apiClient.post('/api/marketplace/auth/register', {
        email: userData.email,
        password: userData.password,
        first_name: userData.firstName,
        last_name: userData.lastName,
        phone: userData.phone,
        is_refresh: true
      });
      
      if (response.data && !response.data.error) {
        // Store token and user data
        localStorage.setItem('marketplace_token', response.data.token);
        if (response.data.refresh_token) {
          localStorage.setItem('marketplace_refresh_token', response.data.refresh_token);
        }
        localStorage.setItem('marketplace_user', JSON.stringify({
          id: response.data.user_id,
          role: response.data.role,
          email: userData.email,
          firstName: userData.firstName,
          lastName: userData.lastName
        }));
      }
      
      return response.data;
    } catch (error) {
      throw error.response?.data || { error: true, message: 'Registration failed' };
    }
  }

  /**
   * Login customer
   * @param {Object} credentials - Login credentials
   * @returns {Promise} API response
   */
  static async login(credentials) {
    try {
      const response = await apiClient.post('/api/marketplace/auth/login', {
        email: credentials.email,
        password: credentials.password,
        is_refresh: true
      });
      
      if (response.data && !response.data.error) {
        // Store token and user data
        localStorage.setItem('marketplace_token', response.data.token);
        if (response.data.refresh_token) {
          localStorage.setItem('marketplace_refresh_token', response.data.refresh_token);
        }
        localStorage.setItem('marketplace_user', JSON.stringify({
          id: response.data.user_id,
          role: response.data.role,
          email: response.data.email,
          firstName: response.data.first_name,
          lastName: response.data.last_name
        }));
      }
      
      return response.data;
    } catch (error) {
      throw error.response?.data || { error: true, message: 'Login failed' };
    }
  }

  /**
   * Get current user profile
   * @returns {Promise} API response
   */
  static async getProfile() {
    try {
      const response = await apiClient.get('/api/marketplace/auth/profile');
      return response.data;
    } catch (error) {
      throw error.response?.data || { error: true, message: 'Failed to get profile' };
    }
  }

  /**
   * Logout customer
   */
  static logout() {
    localStorage.removeItem('marketplace_token');
    localStorage.removeItem('marketplace_refresh_token');
    localStorage.removeItem('marketplace_user');
  }

  /**
   * Check if user is authenticated
   * @returns {boolean}
   */
  static isAuthenticated() {
    return !!localStorage.getItem('marketplace_token');
  }

  /**
   * Get current user from localStorage
   * @returns {Object|null}
   */
  static getCurrentUser() {
    const userStr = localStorage.getItem('marketplace_user');
    return userStr ? JSON.parse(userStr) : null;
  }

  /**
   * Get current token
   * @returns {string|null}
   */
  static getToken() {
    return localStorage.getItem('marketplace_token');
  }
}

export default AuthAPI;
