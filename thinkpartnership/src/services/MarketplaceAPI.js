import apiClient from '../config/api';

class MarketplaceAPI {
  // Get all service categories
  static async getCategories() {
    try {
      const response = await apiClient.get('/api/marketplace/categories');
      
      if (response.data && !response.data.error) {
        return {
          success: true,
          categories: response.data.categories
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'Failed to get categories'
        };
      }
    } catch (error) {
      console.error('Get categories error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to get categories'
      };
    }
  }

  // Get services with filters and pagination
  static async getServices(filters = {}) {
    try {
      const params = new URLSearchParams();
      
      if (filters.category_id) params.append('category_id', filters.category_id);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);
      if (filters.search) params.append('search', filters.search);
      if (filters.status !== undefined) params.append('status', filters.status);

      const response = await apiClient.get(`/api/marketplace/services?${params.toString()}`);
      
      if (response.data && !response.data.error) {
        return {
          success: true,
          services: response.data.services,
          pagination: response.data.pagination
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'Failed to get services'
        };
      }
    } catch (error) {
      console.error('Get services error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to get services'
      };
    }
  }

  // Create a service request
  static async createServiceRequest(requestData) {
    try {
      const response = await apiClient.post('/api/marketplace/service-requests', {
        service_id: requestData.serviceId,
        description: requestData.description,
        preferred_date: requestData.preferredDate,
        budget_range: requestData.budgetRange,
        location: requestData.location,
        urgency: requestData.urgency || 'normal'
      });
      
      if (response.data && !response.data.error) {
        return {
          success: true,
          request: response.data.request,
          message: response.data.message
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'Failed to create service request'
        };
      }
    } catch (error) {
      console.error('Create service request error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to create service request'
      };
    }
  }

  // Get customer's service requests
  static async getServiceRequests() {
    try {
      const response = await apiClient.get('/api/marketplace/service-requests');
      
      if (response.data && !response.data.error) {
        return {
          success: true,
          requests: response.data.requests
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'Failed to get service requests'
        };
      }
    } catch (error) {
      console.error('Get service requests error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to get service requests'
      };
    }
  }

  // Get customer's orders
  static async getOrders() {
    try {
      const response = await apiClient.get('/api/marketplace/orders');
      
      if (response.data && !response.data.error) {
        return {
          success: true,
          orders: response.data.orders
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'Failed to get orders'
        };
      }
    } catch (error) {
      console.error('Get orders error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to get orders'
      };
    }
  }

  // Get service by ID
  static async getServiceById(serviceId) {
    try {
      const response = await apiClient.get(`/api/marketplace/services/${serviceId}`);
      
      if (response.data && !response.data.error) {
        return {
          success: true,
          service: response.data.service
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'Service not found'
        };
      }
    } catch (error) {
      console.error('Get service error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Failed to get service'
      };
    }
  }

  // Search services
  static async searchServices(query, filters = {}) {
    try {
      const params = new URLSearchParams();
      params.append('search', query);
      
      if (filters.category_id) params.append('category_id', filters.category_id);
      if (filters.page) params.append('page', filters.page);
      if (filters.limit) params.append('limit', filters.limit);

      const response = await apiClient.get(`/api/marketplace/services?${params.toString()}`);
      
      if (response.data && !response.data.error) {
        return {
          success: true,
          services: response.data.services,
          pagination: response.data.pagination
        };
      } else {
        return {
          success: false,
          message: response.data.message || 'Search failed'
        };
      }
    } catch (error) {
      console.error('Search services error:', error);
      return {
        success: false,
        message: error.response?.data?.message || 'Search failed'
      };
    }
  }
}

export default MarketplaceAPI;
